const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 格式化日期为 YYYY-MM-DD 格式
const formatDate = date => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = (d.getMonth() + 1).toString().padStart(2, '0')
  const day = d.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 格式化日期为中文格式
const formatDateCN = date => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  return `${year}年${month}月${day}日`
}

// 计算日期差
const daysBetween = (date1, date2) => {
  const oneDay = 24 * 60 * 60 * 1000
  const firstDate = new Date(date1)
  const secondDate = new Date(date2)
  return Math.round(Math.abs((firstDate - secondDate) / oneDay))
}

// 获取状态文本
const getStatusText = status => {
  const statusMap = {
    'unopened': '未开封',
    'opened': '已开封',
    'finished': '已饮尽'
  }
  return statusMap[status] || status
}

// 获取状态颜色
const getStatusColor = status => {
  const colorMap = {
    'unopened': '#52c41a',
    'opened': '#faad14',
    'finished': '#8c8c8c'
  }
  return colorMap[status] || '#8c8c8c'
}

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 数据验证
const validate = {
  // 验证是否为空
  isEmpty: value => {
    return !value || value.toString().trim() === ''
  },

  // 验证是否为数字
  isNumber: value => {
    return !isNaN(value) && !isNaN(parseFloat(value))
  },

  // 验证是否为正数
  isPositiveNumber: value => {
    return validate.isNumber(value) && parseFloat(value) > 0
  },

  // 验证是否为有效日期
  isValidDate: value => {
    return !isNaN(Date.parse(value))
  }
}

module.exports = {
  formatTime,
  formatDate,
  formatDateCN,
  daysBetween,
  getStatusText,
  getStatusColor,
  debounce,
  throttle,
  generateId,
  validate
}
