// 测试随机用户名生成功能

// 形容词数组
const adjectives = [
  '友好的', '开心的', '忙碌的', '勤奋的', '温暖的', '活泼的', '聪明的', '优雅的',
  '热情的', '善良的', '乐观的', '积极的', '创新的', '专注的', '耐心的', '细心的',
  '勇敢的', '坚强的', '温柔的', '幽默的', '机智的', '灵活的', '敏锐的', '果断的',
  '诚实的', '可靠的', '负责的', '谦逊的', '包容的', '理解的', '支持的', '鼓励的',
  '启发的', '激励的', '充满活力的', '富有创意的', '有趣的', '迷人的', '独特的', '特别的',
  '出色的', '杰出的', '卓越的', '非凡的', '令人印象深刻的', '引人注目的', '闪亮的', '光彩的',
  '精彩的', '美妙的', '奇妙的', '神奇的', '惊人的', '了不起的', '超棒的', '完美的'
];

// 咖啡品种数组
const coffeeVarieties = [
  '瑰夏', '蓝山', '铁皮卡', '波旁', '卡杜拉', '卡杜艾', '帕卡马拉', '象豆',
  '艺伎', '玛拉戈吉佩', '维拉萨奇', '卡蒂姆', '卡斯蒂略', '新世界', 'SL28', 'SL34',
  '肯特', '摩卡', '哈拉尔', '西达摩', '耶加雪菲', '科纳', '危地马拉安提瓜', '夏威夷科纳',
  '牙买加蓝山', '埃塞俄比亚', '也门摩卡', '巴拿马瑰夏', '哥伦比亚', '巴西',
  '哥斯达黎加', '危地马拉', '洪都拉斯', '萨尔瓦多', '尼加拉瓜', '秘鲁',
  '厄瓜多尔', '委内瑞拉', '玻利维亚', '墨西哥', '印度尼西亚', '越南',
  '印度', '泰国', '老挝', '缅甸', '菲律宾', '巴布亚新几内亚', '澳大利亚',
  '夏威夷', '加拉帕戈斯', '圣海伦娜', '留尼汪', '马达加斯加', '坦桑尼亚',
  '肯尼亚', '埃塞俄比亚', '卢旺达', '布隆迪', '乌干达', '刚果', '喀麦隆'
];

// 生成随机用户名
function generateRandomUsername() {
  const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const randomCoffee = coffeeVarieties[Math.floor(Math.random() * coffeeVarieties.length)];
  return `${randomAdjective}${randomCoffee}`;
}

// 测试生成20个随机用户名
console.log('=== 随机用户名生成测试 ===');
console.log(`形容词数量: ${adjectives.length}`);
console.log(`咖啡品种数量: ${coffeeVarieties.length}`);
console.log(`理论组合数量: ${adjectives.length * coffeeVarieties.length}`);
console.log('\n生成的随机用户名示例:');

for (let i = 1; i <= 20; i++) {
  const username = generateRandomUsername();
  console.log(`${i.toString().padStart(2, '0')}. ${username}`);
}

console.log('\n=== 测试完成 ===');
