#!/bin/bash

# 创建简单的图标文件
# 使用 ImageMagick 创建基础图标 (需要先安装 ImageMagick)

# 检查是否安装了 ImageMagick
if ! command -v convert &> /dev/null; then
    echo "请先安装 ImageMagick:"
    echo "macOS: brew install imagemagick"
    echo "Ubuntu: sudo apt-get install imagemagick"
    echo "或者使用其他方案下载图标"
    exit 1
fi

# 创建 images 目录
mkdir -p images

# 定义颜色
NORMAL_COLOR="#8B4513"
ACTIVE_COLOR="#D2691E"

echo "开始创建图标..."

# 创建首页图标 (房子形状)
convert -size 81x81 xc:transparent \
    -fill "$NORMAL_COLOR" \
    -draw "polygon 40,20 20,40 25,40 25,65 35,65 35,50 45,50 45,65 55,65 55,40 60,40" \
    images/home.png

convert -size 81x81 xc:transparent \
    -fill "$ACTIVE_COLOR" \
    -draw "polygon 40,20 20,40 25,40 25,65 35,65 35,50 45,50 45,65 55,65 55,40 60,40" \
    images/home_active.png

# 创建图谱图标 (书本形状)
convert -size 81x81 xc:transparent \
    -fill "$NORMAL_COLOR" \
    -draw "rectangle 25,15 55,65" \
    -fill "white" \
    -draw "rectangle 30,25 50,30" \
    -draw "rectangle 30,35 50,40" \
    -draw "rectangle 30,45 45,50" \
    images/atlas.png

convert -size 81x81 xc:transparent \
    -fill "$ACTIVE_COLOR" \
    -draw "rectangle 25,15 55,65" \
    -fill "white" \
    -draw "rectangle 30,25 50,30" \
    -draw "rectangle 30,35 50,40" \
    -draw "rectangle 30,45 45,50" \
    images/atlas_active.png

# 创建豆仓图标 (仓库形状)
convert -size 81x81 xc:transparent \
    -fill "$NORMAL_COLOR" \
    -draw "polygon 40,15 20,35 20,65 60,65 60,35" \
    -fill "white" \
    -draw "rectangle 35,45 45,65" \
    images/storage.png

convert -size 81x81 xc:transparent \
    -fill "$ACTIVE_COLOR" \
    -draw "polygon 40,15 20,35 20,65 60,65 60,35" \
    -fill "white" \
    -draw "rectangle 35,45 45,65" \
    images/storage_active.png

# 创建个人中心图标 (人形)
convert -size 81x81 xc:transparent \
    -fill "$NORMAL_COLOR" \
    -draw "circle 40,30 40,20" \
    -draw "ellipse 40,55 15,20" \
    images/profile.png

convert -size 81x81 xc:transparent \
    -fill "$ACTIVE_COLOR" \
    -draw "circle 40,30 40,20" \
    -draw "ellipse 40,55 15,20" \
    images/profile_active.png

echo "图标创建完成！"
echo "请检查 images/ 目录下的图标文件。"

# 显示创建的文件
ls -la images/*.png
