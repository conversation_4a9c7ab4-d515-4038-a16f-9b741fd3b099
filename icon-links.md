# 图标资源链接

## 免费图标下载链接

### 方案1: Icons8 (推荐)
直接右键保存以下链接的图片：

#### 首页图标
- **home.png**: https://img.icons8.com/ios-filled/81/8B4513/home.png
- **home_active.png**: https://img.icons8.com/ios-filled/81/D2691E/home.png

#### 咖啡图谱图标
- **atlas.png**: https://img.icons8.com/ios-filled/81/8B4513/coffee-beans-.png
- **atlas_active.png**: https://img.icons8.com/ios-filled/81/D2691E/coffee-beans-.png

#### 豆仓图标
- **storage.png**: https://img.icons8.com/ios-filled/81/8B4513/warehouse.png
- **storage_active.png**: https://img.icons8.com/ios-filled/81/D2691E/warehouse.png

#### 个人中心图标
- **profile.png**: https://img.icons8.com/ios-filled/81/8B4513/user.png
- **profile_active.png**: https://img.icons8.com/ios-filled/81/D2691E/user.png

### 方案2: Feather Icons
如果上面的链接不可用，可以使用以下备用链接：

#### 首页图标
- **home.png**: https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/home.svg
- **home_active.png**: 同上，需要修改颜色

#### 咖啡图谱图标
- **atlas.png**: https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/book.svg
- **atlas_active.png**: 同上，需要修改颜色

#### 豆仓图标
- **storage.png**: https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/archive.svg
- **storage_active.png**: 同上，需要修改颜色

#### 个人中心图标
- **profile.png**: https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/user.svg
- **profile_active.png**: 同上，需要修改颜色

## 快速下载方法

### 方法1: 使用浏览器下载
1. 打开上面的链接
2. 右键点击图片
3. 选择"图片另存为"
4. 保存到项目的 `images/` 目录

### 方法2: 使用命令行下载 (macOS/Linux)
```bash
# 进入项目目录
cd /Users/<USER>/Dev/aigc/mycoffee/images

# 下载首页图标
curl -o home.png "https://img.icons8.com/ios-filled/81/8B4513/home.png"
curl -o home_active.png "https://img.icons8.com/ios-filled/81/D2691E/home.png"

# 下载咖啡图谱图标
curl -o atlas.png "https://img.icons8.com/ios-filled/81/8B4513/coffee-beans-.png"
curl -o atlas_active.png "https://img.icons8.com/ios-filled/81/D2691E/coffee-beans-.png"

# 下载豆仓图标
curl -o storage.png "https://img.icons8.com/ios-filled/81/8B4513/warehouse.png"
curl -o storage_active.png "https://img.icons8.com/ios-filled/81/D2691E/warehouse.png"

# 下载个人中心图标
curl -o profile.png "https://img.icons8.com/ios-filled/81/8B4513/user.png"
curl -o profile_active.png "https://img.icons8.com/ios-filled/81/D2691E/user.png"
```

### 方法3: 使用 Node.js 脚本
运行项目根目录下的 `download-icons.js` 文件：
```bash
node download-icons.js
```

## 图标规格要求

- **尺寸**: 81px × 81px
- **格式**: PNG
- **背景**: 透明
- **颜色**: 
  - 未选中状态: #8B4513 (深棕色)
  - 选中状态: #D2691E (橙棕色)

## 验证图标

下载完成后，请确保 `images/` 目录包含以下8个文件：
- home.png
- home_active.png
- atlas.png
- atlas_active.png
- storage.png
- storage_active.png
- profile.png
- profile_active.png

## 备用方案

如果以上方案都不可用，你也可以：

1. 使用 Figma、Sketch 等设计工具自己制作
2. 在 Iconfont、Flaticon 等网站搜索相关图标
3. 使用 AI 工具生成图标
4. 暂时使用纯色方块作为占位符，后续再替换
