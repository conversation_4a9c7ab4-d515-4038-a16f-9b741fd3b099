# getUserInfo 逻辑优化更新

## 修改内容

### 🔄 新的用户信息获取流程

1. **检查登录状态**
   - 验证是否有 `openid`
   - 如果没有，抛出错误提示用户未登录

2. **优先从云端查询**
   - 使用 `openid` 在云数据库 `users` 集合中查询
   - 如果找到现有用户信息，直接使用

3. **生成新用户信息**（仅当云端不存在时）
   - 生成随机用户名（形容词 + 咖啡品种）
   - 创建完整的用户信息对象
   - 保存到云数据库

4. **更新本地状态**
   - 更新全局状态 `globalData`
   - 保存到本地存储
   - 设置登录状态为 true

## 🔧 技术实现

### getUserInfo 方法
```javascript
getUserInfo() {
  return new Promise(async (resolve, reject) => {
    try {
      // 1. 检查 openid
      if (!this.globalData.openid) {
        throw new Error('用户未登录，缺少 openid')
      }

      // 2. 查询云端用户信息
      const db = wx.cloud.database()
      const queryResult = await db.collection('users').where({
        _openid: this.globalData.openid
      }).get()

      let userInfo

      if (queryResult.data && queryResult.data.length > 0) {
        // 3a. 使用现有用户信息
        userInfo = queryResult.data[0]
      } else {
        // 3b. 生成新用户信息
        userInfo = {
          nickName: this.generateRandomUsername(),
          avatarUrl: '/images/profile.png',
          // ... 其他字段
        }
        await this.saveUserToCloud(userInfo)
      }
      
      // 4. 更新本地状态
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
      wx.setStorageSync('userInfo', userInfo)

      resolve(userInfo)
    } catch (error) {
      reject(error)
    }
  })
}
```

### saveUserToCloud 方法优化
- 改为返回 Promise
- 使用 async/await 语法
- 更好的错误处理
- 支持新增和更新操作

## 📊 用户体验改进

### 首次登录用户
1. 系统检测到云端无用户信息
2. 自动生成随机咖啡主题用户名
3. 保存到云端和本地
4. 完成登录

### 老用户登录
1. 系统从云端获取现有用户信息
2. 直接使用已有的用户名和设置
3. 更新本地状态
4. 完成登录

## 🔒 数据一致性保证

### 云端优先策略
- 云端数据作为权威数据源
- 本地存储仅作为缓存
- 每次登录都会同步云端数据

### 错误处理
- 网络错误时的降级处理
- 详细的错误日志记录
- 用户友好的错误提示

## 🚀 性能优化

### 减少重复操作
- 避免重复生成用户名
- 复用现有用户信息
- 减少不必要的数据库写入

### 异步操作
- 使用 async/await 提高代码可读性
- Promise 链式调用优化
- 更好的错误传播机制

## 🧪 测试场景

### 场景1：新用户首次登录
```
输入：openid = "new_user_123"
云端查询：无记录
结果：生成随机用户名，保存到云端
```

### 场景2：老用户重新登录
```
输入：openid = "existing_user_456"
云端查询：找到记录 { nickName: "友好的瑰夏", ... }
结果：使用现有用户名
```

### 场景3：网络错误
```
输入：openid = "user_789"
云端查询：网络超时
结果：抛出错误，提示用户重试
```

## 📝 注意事项

1. **依赖关系**
   - 必须先调用 `login()` 获取 `openid`
   - 确保云开发环境已正确配置

2. **数据库权限**
   - 确保 `users` 集合权限设置为"仅创建者可读写"
   - 验证云开发环境配置正确

3. **错误处理**
   - 网络异常时的用户提示
   - 数据库操作失败的回退机制

## 🔮 未来扩展

1. **离线支持**
   - 网络不可用时使用本地缓存
   - 网络恢复后同步数据

2. **用户信息编辑**
   - 允许用户修改昵称
   - 头像选择功能

3. **数据迁移**
   - 支持从其他平台导入用户数据
   - 用户数据导出功能
