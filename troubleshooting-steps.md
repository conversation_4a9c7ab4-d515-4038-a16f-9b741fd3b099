# Foundation.onLoad 错误故障排除步骤

## 立即尝试的解决方案

### 步骤 1: 清除缓存 (最重要)
1. 在微信开发者工具中：
   - 点击菜单栏 **"项目"** → **"清缓存"** → **"清除所有缓存"**
   - 或者使用快捷键 `Ctrl+Shift+R` (Windows) 或 `Cmd+Shift+R` (Mac)
2. 重新编译项目
3. 重启微信开发者工具

### 步骤 2: 检查基础库版本
1. 点击开发者工具右上角的 **"详情"**
2. 在 **"本地设置"** 中检查基础库版本
3. 确保使用 **2.19.4** 或更高版本
4. 如果版本过低，请更新

### 步骤 3: 临时简化项目 (用于定位问题)
1. 备份当前的 `app.js` 文件
2. 将项目根目录下的 `app-simple.js` 重命名为 `app.js`
3. 临时修改 `app.json`，只保留首页：
```json
{
  "pages": [
    "pages/index/index"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#8B4513",
    "navigationBarTitleText": "我的咖啡",
    "navigationBarTextStyle": "white",
    "backgroundColor": "#F5F5DC"
  },
  "style": "v2",
  "sitemapLocation": "sitemap.json"
}
```
4. 测试是否还有错误

### 步骤 4: 检查云开发配置
如果使用了云开发功能：
1. 确认已在微信公众平台开通云开发服务
2. 在开发者工具中点击 **"云开发"** 按钮
3. 确认云开发环境已创建并激活
4. 如果有环境ID，在 `app.js` 中取消注释并填入：
```javascript
wx.cloud.init({
  env: 'your-env-id', // 填入你的环境ID
  traceUser: true,
})
```

### 步骤 5: 逐步恢复功能
如果简化版本正常工作：
1. 恢复原始的 `app.js`
2. 在 `app.json` 中逐个添加页面：
```json
{
  "pages": [
    "pages/index/index",
    "pages/atlas/atlas"
    // 先添加一个，测试正常后再添加下一个
  ]
}
```
3. 找出导致问题的具体页面

## 如果问题仍然存在

### 检查控制台错误信息
1. 打开开发者工具的 **"调试器"**
2. 查看 **"Console"** 标签页
3. 记录所有错误信息

### 检查网络连接
1. 确认网络连接正常
2. 检查是否有防火墙阻止连接
3. 尝试切换网络环境

### 重新创建项目
如果以上步骤都无效：
1. 创建一个新的小程序项目
2. 逐步复制代码文件
3. 测试每个文件添加后是否正常

## 常见原因和解决方案

### 1. 云开发未正确初始化
**症状**: 涉及云开发的页面无法加载
**解决**: 确保云开发服务已开通并正确配置

### 2. 页面路径错误
**症状**: 特定页面无法访问
**解决**: 检查 `app.json` 中的页面路径是否与实际文件路径一致

### 3. JavaScript 语法错误
**症状**: 页面加载时出现错误
**解决**: 检查所有 `.js` 文件的语法

### 4. 依赖文件缺失
**症状**: 引用的文件找不到
**解决**: 确保所有 `require()` 的文件都存在

## 预防措施

1. **定期清理缓存**: 每次重大修改后清理缓存
2. **版本控制**: 使用 Git 等工具管理代码版本
3. **渐进式开发**: 每次只添加一个功能，确保稳定后再继续
4. **错误处理**: 在关键代码处添加 try-catch 错误处理

## 联系支持

如果问题仍然无法解决，请提供：
1. 微信开发者工具版本号
2. 基础库版本号
3. 完整的错误信息截图
4. 项目的基本配置信息
