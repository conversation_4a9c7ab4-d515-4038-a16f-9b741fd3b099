// app.js - 简化版本用于测试
App({
  onLaunch() {
    console.log('App onLaunch 开始')
    
    // 最基本的初始化
    try {
      // 展示本地存储能力
      const logs = wx.getStorageSync('logs') || []
      logs.unshift(Date.now())
      wx.setStorageSync('logs', logs)
      console.log('本地存储初始化成功')
    } catch (error) {
      console.error('本地存储初始化失败', error)
    }

    console.log('App onLaunch 完成')
  },

  globalData: {
    userInfo: null,
    isLoggedIn: false,
    openid: null
  }
})
