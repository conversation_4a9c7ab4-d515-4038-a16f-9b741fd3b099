// 测试 getUserInfo 逻辑的模拟代码

// 模拟微信云开发 API
const mockWxCloud = {
  database: () => ({
    collection: (name) => ({
      where: (condition) => ({
        get: async () => {
          console.log(`查询集合 ${name}，条件:`, condition)
          
          // 模拟不同的查询结果
          if (condition._openid === 'existing_user') {
            return {
              data: [{
                _id: 'user_doc_id',
                _openid: 'existing_user',
                nickName: '友好的瑰夏',
                avatarUrl: '/images/profile.png',
                createTime: new Date('2024-01-01'),
                updateTime: new Date('2024-01-01')
              }]
            }
          } else {
            return { data: [] } // 新用户，无记录
          }
        }
      }),
      add: async (data) => {
        console.log('添加新用户:', data)
        return { _id: 'new_user_doc_id' }
      }
    })
  })
}

// 模拟 App 对象的部分方法
const mockApp = {
  globalData: {
    openid: null,
    userInfo: null,
    isLoggedIn: false
  },

  adjectives: ['友好的', '开心的', '温暖的', '活泼的'],
  coffeeVarieties: ['瑰夏', '蓝山', '铁皮卡', '波旁'],

  generateRandomUsername() {
    const randomAdjective = this.adjectives[Math.floor(Math.random() * this.adjectives.length)]
    const randomCoffee = this.coffeeVarieties[Math.floor(Math.random() * this.coffeeVarieties.length)]
    return `${randomAdjective}${randomCoffee}`
  },

  async saveUserToCloud(userInfo) {
    console.log('保存用户信息到云端:', userInfo)
    const db = mockWxCloud.database()
    const users = db.collection('users')
    return await users.add({ data: userInfo })
  },

  async getUserInfo() {
    console.log("开始获取用户信息...")

    try {
      // 检查 openid
      if (!this.globalData.openid) {
        throw new Error('用户未登录，缺少 openid')
      }

      // 查询云端用户信息
      const db = mockWxCloud.database()
      const users = db.collection('users')
      
      console.log('正在查询云端用户信息...')
      const queryResult = await users.where({
        _openid: this.globalData.openid
      }).get()

      let userInfo

      if (queryResult.data && queryResult.data.length > 0) {
        // 用户已存在
        userInfo = queryResult.data[0]
        console.log('✅ 从云端获取到现有用户信息:', userInfo.nickName)
      } else {
        // 生成新用户
        console.log('🆕 云端未找到用户信息，生成新的随机用户名...')
        const randomUsername = this.generateRandomUsername()
        userInfo = {
          nickName: randomUsername,
          avatarUrl: '/images/profile.png',
          gender: 0,
          country: '',
          province: '',
          city: '',
          language: 'zh_CN'
        }
        
        await this.saveUserToCloud(userInfo)
        console.log('✅ 新用户信息已保存到云端:', userInfo.nickName)
      }
      
      // 更新全局状态
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true

      console.log('🎉 用户信息获取成功:', userInfo.nickName)
      return userInfo
      
    } catch (error) {
      console.error('❌ 获取用户信息失败', error.message)
      throw error
    }
  }
}

// 测试函数
async function testGetUserInfo() {
  console.log('=== getUserInfo 逻辑测试 ===\n')

  // 测试1: 无 openid 的情况
  console.log('📋 测试1: 无 openid 的情况')
  try {
    await mockApp.getUserInfo()
  } catch (error) {
    console.log('✅ 正确捕获错误:', error.message)
  }
  console.log('')

  // 测试2: 现有用户
  console.log('📋 测试2: 现有用户登录')
  mockApp.globalData.openid = 'existing_user'
  try {
    const userInfo = await mockApp.getUserInfo()
    console.log('✅ 现有用户登录成功')
  } catch (error) {
    console.log('❌ 现有用户登录失败:', error.message)
  }
  console.log('')

  // 测试3: 新用户
  console.log('📋 测试3: 新用户登录')
  mockApp.globalData.openid = 'new_user'
  mockApp.globalData.userInfo = null
  mockApp.globalData.isLoggedIn = false
  try {
    const userInfo = await mockApp.getUserInfo()
    console.log('✅ 新用户登录成功')
  } catch (error) {
    console.log('❌ 新用户登录失败:', error.message)
  }

  console.log('\n=== 测试完成 ===')
}

// 运行测试
testGetUserInfo()
