# 图片资源目录

请在此目录下放置以下图片文件：

## TabBar 图标

### 需要的文件
- `home.png` - 首页图标（未选中状态）
- `home_active.png` - 首页图标（选中状态）
- `atlas.png` - 咖啡图谱图标（未选中状态）
- `atlas_active.png` - 咖啡图谱图标（选中状态）
- `storage.png` - 豆仓图标（未选中状态）
- `storage_active.png` - 豆仓图标（选中状态）
- `profile.png` - 个人中心图标（未选中状态）
- `profile_active.png` - 个人中心图标（选中状态）

## 图标规格
- **尺寸**: 81px × 81px
- **格式**: PNG
- **背景**: 透明
- **颜色**:
  - 未选中状态: #8B4513 (深棕色)
  - 选中状态: #D2691E (橙棕色)

## 获取图标的方法

### 方法1: 手动下载 (推荐)
访问以下链接，右键保存图片：

#### 首页图标
- [home.png](https://img.icons8.com/ios-filled/81/8B4513/home.png)
- [home_active.png](https://img.icons8.com/ios-filled/81/D2691E/home.png)

#### 咖啡图谱图标
- [atlas.png](https://img.icons8.com/ios-filled/81/8B4513/coffee-beans-.png)
- [atlas_active.png](https://img.icons8.com/ios-filled/81/D2691E/coffee-beans-.png)

#### 豆仓图标
- [storage.png](https://img.icons8.com/ios-filled/81/8B4513/warehouse.png)
- [storage_active.png](https://img.icons8.com/ios-filled/81/D2691E/warehouse.png)

#### 个人中心图标
- [profile.png](https://img.icons8.com/ios-filled/81/8B4513/user.png)
- [profile_active.png](https://img.icons8.com/ios-filled/81/D2691E/user.png)

### 方法2: 使用 HTML 生成器
1. 在浏览器中打开 `create-svg-icons.html`
2. 点击各个图标下方的下载按钮
3. 将下载的文件放入此目录

### 方法3: 命令行下载
```bash
# 进入 images 目录
cd images

# 下载所有图标
curl -o home.png "https://img.icons8.com/ios-filled/81/8B4513/home.png"
curl -o home_active.png "https://img.icons8.com/ios-filled/81/D2691E/home.png"
curl -o atlas.png "https://img.icons8.com/ios-filled/81/8B4513/coffee-beans-.png"
curl -o atlas_active.png "https://img.icons8.com/ios-filled/81/D2691E/coffee-beans-.png"
curl -o storage.png "https://img.icons8.com/ios-filled/81/8B4513/warehouse.png"
curl -o storage_active.png "https://img.icons8.com/ios-filled/81/D2691E/warehouse.png"
curl -o profile.png "https://img.icons8.com/ios-filled/81/8B4513/user.png"
curl -o profile_active.png "https://img.icons8.com/ios-filled/81/D2691E/user.png"
```

### 方法4: 使用 Node.js 脚本
在项目根目录运行：
```bash
node download-icons.js
```

## 验证图标

下载完成后，请确保此目录包含以下 8 个文件：
- ✅ home.png
- ✅ home_active.png
- ✅ atlas.png
- ✅ atlas_active.png
- ✅ storage.png
- ✅ storage_active.png
- ✅ profile.png
- ✅ profile_active.png

## 注意事项

1. **文件名必须完全匹配**（区分大小写）
2. **图标尺寸必须为 81x81 像素**
3. **背景必须是透明的**
4. **如果下载失败，可以先使用纯色方块作为占位符**

## 其他图片资源
可以在此目录下添加其他项目所需的图片资源。
