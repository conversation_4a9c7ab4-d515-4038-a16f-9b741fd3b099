# 数据库设置指南

## 云数据库集合创建

在微信云开发控制台中，需要创建以下数据库集合：

### 1. users 集合
用于存储用户信息

**权限设置**: 仅创建者可读写

**索引建议**:
- `_openid` (唯一索引)
- `createTime` (普通索引)

### 2. coffee_atlas 集合
用于存储咖啡图谱信息

**权限设置**: 仅创建者可读写

**索引建议**:
- `_openid` (普通索引)
- `brand` (普通索引)
- `name` (普通索引)
- `origin` (普通索引)
- `createTime` (普通索引)

### 3. coffee_storage 集合
用于存储豆仓记录

**权限设置**: 仅创建者可读写

**索引建议**:
- `_openid` (普通索引)
- `coffeeId` (普通索引)
- `status` (普通索引)
- `createTime` (普通索引)
- `purchaseDate` (普通索引)

## 数据库权限配置

为了确保数据安全，建议设置以下权限规则：

### users 集合权限
```javascript
{
  "read": "auth.openid == resource._openid",
  "write": "auth.openid == resource._openid"
}
```

### coffee_atlas 集合权限
```javascript
{
  "read": "auth.openid == resource._openid",
  "write": "auth.openid == resource._openid"
}
```

### coffee_storage 集合权限
```javascript
{
  "read": "auth.openid == resource._openid",
  "write": "auth.openid == resource._openid"
}
```

## 示例数据

### 咖啡图谱示例数据
```javascript
{
  "_openid": "用户的openid",
  "brand": "蓝山",
  "name": "牙买加蓝山一号",
  "variety": "铁皮卡",
  "origin": "牙买加蓝山",
  "estate": "克利夫顿庄园",
  "process": "水洗",
  "roastLevel": "中烘",
  "flavor": "口感醇厚，带有巧克力和坚果的香味，酸度适中，回甘持久",
  "price": 298,
  "grindSize": "中细研磨",
  "rating": 5,
  "review": "非常棒的咖啡，值得收藏",
  "purchaseLink": "https://example.com/coffee/1",
  "createTime": new Date(),
  "updateTime": new Date()
}
```

### 豆仓记录示例数据
```javascript
{
  "_openid": "用户的openid",
  "coffeeId": "关联的咖啡ID",
  "coffeeName": "牙买加蓝山一号",
  "brand": "蓝山",
  "purchaseDate": new Date("2024-01-15"),
  "openDate": new Date("2024-01-20"),
  "finishDate": null,
  "weight": 250,
  "price": 298,
  "status": "opened",
  "notes": "春节期间购买，品质很好",
  "createTime": new Date(),
  "updateTime": new Date()
}
```

## 数据库操作最佳实践

### 1. 查询优化
- 使用索引字段进行查询
- 避免全表扫描
- 合理使用 limit 限制返回数据量

### 2. 数据一致性
- 在删除咖啡图谱时，检查是否有关联的豆仓记录
- 更新操作时同时更新 updateTime 字段

### 3. 错误处理
- 所有数据库操作都应该有错误处理
- 向用户提供友好的错误提示

### 4. 性能优化
- 批量操作时使用事务
- 定期清理无用数据
- 监控数据库性能指标

## 数据迁移

如果需要从其他系统迁移数据，可以参考以下步骤：

1. 导出原系统数据
2. 转换数据格式
3. 批量导入到云数据库
4. 验证数据完整性
5. 更新应用配置

## 备份策略

建议定期备份重要数据：

1. 使用云开发控制台的导出功能
2. 定期下载数据备份
3. 测试备份数据的完整性
4. 制定数据恢复计划
