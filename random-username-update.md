# 随机用户名生成功能更新

## 修改内容

### 1. 替换微信用户信息获取
- 移除了 `wx.getUserProfile()` 调用
- 不再从微信获取用户的真实头像和昵称
- 改为生成随机用户名

### 2. 添加随机用户名生成逻辑

#### 形容词数组 (56个)
包含正面、积极的形容词：
- 基础形容词：友好的、开心的、忙碌的、勤奋的、温暖的等
- 性格特征：活泼的、聪明的、优雅的、热情的、善良的等
- 品质特征：乐观的、积极的、创新的、专注的、耐心的等
- 能力特征：勇敢的、坚强的、机智的、灵活的、敏锐的等
- 优秀品质：诚实的、可靠的、负责的、谦逊的、包容的等
- 特殊形容词：充满活力的、富有创意的、令人印象深刻的等

#### 咖啡品种数组 (60+个)
包含各种咖啡品种和产地：
- 经典品种：瑰夏、蓝山、铁皮卡、波旁、卡杜拉等
- 特殊品种：艺伎、玛拉戈吉佩、帕卡马拉、象豆等
- 产地品种：SL28、SL34、肯特、摩卡、哈拉尔等
- 知名产地：耶加雪菲、科纳、安提瓜、夏威夷科纳等
- 各国咖啡：哥伦比亚、巴西、危地马拉、洪都拉斯等

### 3. 用户名生成规则
- 格式：`[形容词] + [咖啡品种]`
- 示例：
  - 友好的瑰夏
  - 开心的蓝山
  - 温暖的铁皮卡
  - 活泼的波旁

### 4. 用户信息结构
```javascript
{
  nickName: "随机生成的用户名",
  avatarUrl: "/images/profile.png", // 使用默认头像
  gender: 0, // 未知
  country: "",
  province: "",
  city: "",
  language: "zh_CN"
}
```

## 功能特点

### 优点
1. **隐私保护**：不获取用户真实信息
2. **趣味性**：生成有趣的咖啡主题用户名
3. **一致性**：所有用户都有统一的头像风格
4. **简化流程**：无需用户授权，直接生成

### 用户体验
1. **即时登录**：无需等待用户授权
2. **主题一致**：用户名与咖啡应用主题相符
3. **个性化**：每个用户都有独特的随机用户名
4. **无压力**：用户无需担心隐私泄露

## 技术实现

### 随机算法
- 使用 `Math.random()` 和 `Math.floor()` 进行随机选择
- 从两个数组中分别随机选择一个元素进行组合

### 错误处理
- 使用 try-catch 包装生成逻辑
- 提供详细的错误日志
- 确保生成失败时有适当的错误处理

### 数据存储
- 本地存储：使用 `wx.setStorageSync()` 保存到本地
- 云端存储：调用 `saveUserToCloud()` 保存到云数据库
- 全局状态：更新 `globalData` 中的用户信息

## 使用方法

用户登录时，系统会自动：
1. 调用 `generateRandomUsername()` 生成随机用户名
2. 创建包含随机用户名的用户信息对象
3. 保存用户信息到本地和云端
4. 更新全局登录状态

## 注意事项

1. **头像文件**：确保 `/images/profile.png` 文件存在
2. **数组扩展**：可以根据需要添加更多形容词和咖啡品种
3. **重复性**：理论上可能生成重复用户名，但概率很低（56 × 60+ = 3360+ 种组合）
4. **本地化**：当前使用中文，如需支持其他语言需要添加对应数组

## 未来扩展

1. **用户名唯一性检查**：检查生成的用户名是否已存在
2. **用户名自定义**：允许用户后续修改用户名
3. **头像选择**：提供多个默认头像供用户选择
4. **多语言支持**：支持英文等其他语言的随机用户名生成
