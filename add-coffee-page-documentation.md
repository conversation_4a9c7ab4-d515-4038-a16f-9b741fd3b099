# 添加咖啡页面功能文档

## 📄 页面概述

新创建的 `pages/add-coffee/add-coffee` 页面是一个专门用于添加和编辑咖啡豆信息的完整表单页面，提供比首页快捷添加更详细和专业的功能。

## 🎯 功能特性

### 1. 双模式支持
- **添加模式**：创建新的咖啡记录
- **编辑模式**：修改现有咖啡记录

### 2. 完整的表单字段
#### 基本信息
- **品牌** *（必填）
- **咖啡名称** *（必填）
- **产地** *（必填）
- **品种**（可选）
- **庄园**（可选）
- **处理法**（选择器）
- **烘焙度**（选择器）
- **价格**（可选）

#### 风味描述
- **风味**（多行文本，200字限制）

#### 个人评价
- **评分**（1-5星）
- **评价**（多行文本，300字限制）

#### 其他信息
- **推荐研磨度**（选择器）
- **购买链接**（可选）

### 3. 智能选择器
- **处理法选项**：水洗、日晒、蜜处理、湿刨法、厌氧发酵、碳酸浸渍、其他
- **烘焙度选项**：浅烘、中浅烘、中烘、中深烘、深烘
- **研磨度选项**：极细研磨、细研磨、中细研磨、中研磨、中粗研磨、粗研磨

## 🎨 界面设计

### 页面布局
```
┌─────────────────────────────────────┐
│ ‹ 返回      添加咖啡/编辑咖啡        │
├─────────────────────────────────────┤
│                                     │
│ 基本信息                            │
│ ─────────────────────────────────── │
│ [品牌*] [名称*] [产地*] [品种]      │
│ [庄园] [处理法] [烘焙度] [价格]     │
│                                     │
│ 风味描述                            │
│ ─────────────────────────────────── │
│ [风味描述文本框]                    │
│                                     │
│ 个人评价                            │
│ ─────────────────────────────────── │
│ [评分] ⭐⭐⭐⭐⭐                    │
│ [个人评价文本框]                    │
│                                     │
│ 其他信息                            │
│ ─────────────────────────────────── │
│ [推荐研磨度] [购买链接]             │
│                                     │
├─────────────────────────────────────┤
│ [取消]           [保存/更新]        │
└─────────────────────────────────────┘
```

### 设计特点
- **全屏布局**：充分利用屏幕空间
- **分组设计**：逻辑清晰的信息分组
- **统一样式**：与应用整体设计保持一致
- **响应式**：适配不同屏幕尺寸

## 🔧 技术实现

### 数据结构
```javascript
formData: {
  brand: '',        // 品牌
  name: '',         // 咖啡名称
  variety: '',      // 品种
  origin: '',       // 产地
  estate: '',       // 庄园
  process: '',      // 处理法
  roastLevel: '',   // 烘焙度
  flavor: '',       // 风味描述
  price: '',        // 价格
  grindSize: '',    // 推荐研磨度
  rating: 0,        // 评分
  review: '',       // 个人评价
  purchaseLink: ''  // 购买链接
}
```

### 核心方法
- **loadCoffeeData()**: 加载现有咖啡数据（编辑模式）
- **validateForm()**: 表单验证
- **saveCoffee()**: 保存咖啡信息
- **onInputChange()**: 处理输入变化
- **onRatingChange()**: 处理评分变化

### 数据库操作
```javascript
// 添加新记录
await db.collection('coffee_atlas').add({
  data: {
    ...formData,
    createTime: new Date(),
    updateTime: new Date()
  }
})

// 更新现有记录
await db.collection('coffee_atlas').doc(coffeeId).update({
  data: {
    ...formData,
    updateTime: new Date()
  }
})
```

## 🔄 页面集成

### 入口页面
1. **咖啡图谱页面**
   - 添加按钮：跳转到添加模式
   - 编辑按钮：跳转到编辑模式

2. **首页快捷添加**
   - 保持原有功能，提供快速添加选项

### 导航流程
```
咖啡图谱页面 → 点击添加 → add-coffee页面（添加模式）
咖啡图谱页面 → 点击编辑 → add-coffee页面（编辑模式）
add-coffee页面 → 保存成功 → 返回上一页
```

## 🎯 用户体验

### 表单验证
- **必填字段检查**：品牌、名称、产地
- **实时反馈**：输入时边框颜色变化
- **错误提示**：清晰的错误信息

### 操作反馈
- **加载状态**：数据加载时显示loading
- **保存状态**：提交时按钮显示loading
- **成功提示**：操作成功后显示toast
- **自动返回**：保存成功后自动返回上一页

### 数据持久化
- **自动保存**：表单数据实时更新到data
- **云端存储**：数据保存到云数据库
- **时间戳**：自动记录创建和更新时间

## 📱 移动端优化

### 输入体验
- **键盘适配**：数字字段使用数字键盘
- **文本限制**：风味描述200字，评价300字
- **滚动支持**：长表单支持垂直滚动

### 触摸交互
- **星级评分**：大尺寸星星，易于点击
- **选择器**：清晰的下拉选择界面
- **按钮设计**：足够大的点击区域

## 🔮 扩展功能

### 未来可能的增强
1. **图片上传**：支持咖啡豆照片
2. **模板功能**：保存和使用表单模板
3. **批量导入**：从文件导入咖啡信息
4. **分享功能**：分享咖啡信息到社交平台
5. **标签系统**：为咖啡添加自定义标签
6. **收藏功能**：标记喜爱的咖啡

### 数据分析
1. **统计报告**：个人咖啡偏好分析
2. **推荐系统**：基于历史数据推荐新咖啡
3. **价格趋势**：咖啡价格变化分析

这个新页面提供了完整而专业的咖啡信息管理功能，是咖啡爱好者记录和管理咖啡收藏的理想工具。
