// app.js
App({
  globalData: {
    loginPromise: null,
    isLaunchFinished: false,
    // 默认用户信息
    userInfo: {
      name: "咖啡爱好者"
    },
    isLoggedIn: false,
    openid: null
  },
  onLaunch() {
    try {
      // 初始化云开发
      if (!wx.cloud) {
        console.error('请使用 2.2.3 或以上的基础库以使用云能力')
      } else {
        wx.cloud.init({
          // env 参数说明：
          //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
          //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
          //   如不填则使用默认环境（第一个创建的环境）
          // env: 'my-env-id',
          traceUser: true,
        })
        console.log('云开发初始化成功')
      }
    } catch (error) {
      console.error('云开发初始化失败', error)
    }

    try {
      // 展示本地存储能力
      const logs = wx.getStorageSync('logs') || []
      logs.unshift(Date.now())
      wx.setStorageSync('logs', logs)
    } catch (error) {
      console.error('本地存储初始化失败', error)
    }

    // 获取 openid 后获取用户信息，标记启动完成
    this.login().then(() => {
        this.getUserInfo().then(() => {
          this.globalData.isLaunchFinished = true
        }).catch((error) => {
          console.error('获取用户信息失败', error)
        })
    })
  },

  // 检查登录状态
  initUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
    } else {
      this.globalData.userInfo = {}
    }
  },

  // 获取用户的 openid
  login() {
    return new Promise((resolve, reject) => {
      console.log("app.login")
      wx.cloud.callFunction({
        name: "quickstartFunctions",
        data: {
          cmd: "getOpenId"
        },
      }).then(
          (res) => {
            // console.log("app.login res", res)
            this.globalData.openid = res.result.openid
            console.log("login openid is", res.result.openid)
            this.globalData.isLoggedIn = true
            resolve(res.result)
          }
      ).catch(
          (e) => {
            console.error("app.login error", e)
            reject(e)
          }
      )
    })
  },

  // 形容词数组
  adjectives: [
    '友好的', '开心的', '忙碌的', '勤奋的', '温暖的', '活泼的', '聪明的', '优雅的',
    '热情的', '善良的', '乐观的', '积极的', '创新的', '专注的', '耐心的', '细心的',
    '勇敢的', '坚强的', '温柔的', '幽默的', '机智的', '灵活的', '敏锐的', '果断的',
    '诚实的', '可靠的', '负责的', '谦逊的', '包容的', '理解的', '支持的', '鼓励的',
    '启发的', '激励的', '充满活力的', '富有创意的', '有趣的', '迷人的', '独特的', '特别的',
    '出色的', '杰出的', '卓越的', '非凡的', '令人印象深刻的', '引人注目的', '闪亮的', '光彩的',
    '精彩的', '美妙的', '奇妙的', '神奇的', '惊人的', '了不起的', '超棒的', '完美的'
  ],

  // 咖啡品种数组
  coffeeVarieties: [
    '瑰夏', '蓝山', '铁皮卡', '波旁', '卡杜拉', '卡杜艾', '帕卡马拉', '象豆',
    '艺伎', '玛拉戈吉佩', '维拉萨奇', '卡蒂姆', '卡斯蒂略', '新世界', 'SL28', 'SL34',
    '肯特', '摩卡', '哈拉尔', '西达摩', '耶加雪菲', '科纳', '危地马拉安提瓜', '夏威夷科纳',
    '牙买加蓝山', '埃塞俄比亚', '也门摩卡', '巴拿马瑰夏', '哥伦比亚', '巴西',
    '哥斯达黎加', '危地马拉', '洪都拉斯', '萨尔瓦多', '尼加拉瓜', '秘鲁',
    '厄瓜多尔', '委内瑞拉', '玻利维亚', '墨西哥', '印度尼西亚', '越南',
    '印度', '泰国', '老挝', '缅甸', '菲律宾', '巴布亚新几内亚', '澳大利亚',
    '夏威夷', '加拉帕戈斯', '圣海伦娜', '留尼汪', '马达加斯加', '坦桑尼亚',
    '肯尼亚', '埃塞俄比亚', '卢旺达', '布隆迪', '乌干达', '刚果', '喀麦隆'
  ],

  // 生成随机用户名
  generateRandomUsername() {
    const randomAdjective = this.adjectives[Math.floor(Math.random() * this.adjectives.length)]
    const randomCoffee = this.coffeeVarieties[Math.floor(Math.random() * this.coffeeVarieties.length)]
    return `${randomAdjective}${randomCoffee}`
  },

  // 获取用户信息（优先从云端获取，不存在则生成随机用户名）
  getUserInfo() {
    return new Promise(async (resolve, reject) => {
      console.log("app.getUserInfo")

      try {
        // 确保已经有 openid
        if (!this.globalData.openid) {
          throw new Error('用户未登录，缺少 openid')
        }

        // 首先尝试从云数据库获取现有用户信息
        const db = wx.cloud.database()
        const users = db.collection('users')

        console.log('正在查询云端用户信息...')
        const queryResult = await users.where({
          _openid: this.globalData.openid
        }).get()

        let userInfo

        if (queryResult.data && queryResult.data.length > 0) {
          // 用户已存在，使用现有信息
          userInfo = queryResult.data[0]
          console.log('从云端获取到现有用户信息:', userInfo)
        } else {
          // 用户不存在，生成新的随机用户信息
          console.log('云端未找到用户信息，生成新的随机用户名...')
          const randomUsername = this.generateRandomUsername()
          userInfo = {
            nickName: randomUsername,
          }

          // 保存新用户到云数据库
          await this.saveUserToCloud(userInfo)
          console.log('新用户信息已保存到云端:', userInfo)
        }

        // 更新全局状态和本地存储
        this.globalData.userInfo = userInfo
        this.globalData.isLoggedIn = true
        wx.setStorageSync('userInfo', userInfo)
        console.log('用户信息获取成功:', userInfo)
        resolve(userInfo)
      } catch (error) {
        console.error('获取用户信息失败', error)
        reject(error)
      }
    })
  },

  // 保存用户信息到云数据库
  saveUserToCloud(userInfo) {
    return new Promise(async (resolve, reject) => {
      try {
        const db = wx.cloud.database()
        const users = db.collection('users')

        // 查询用户是否已存在
        const queryResult = await users.where({
          _openid: this.globalData.openid
        }).get()

        if (queryResult.data.length === 0) {
          // 新用户，创建记录
          const addResult = await users.add({
            data: {
              ...userInfo,
              createTime: new Date(),
              updateTime: new Date()
            }
          })
          console.log('用户信息保存成功', addResult)
          resolve(addResult)
        } else {
          // 已存在用户，更新信息
          const updateResult = await users.doc(queryResult.data[0]._id).update({
            data: {
              ...userInfo,
              updateTime: new Date()
            }
          })
          console.log('用户信息更新成功', updateResult)
          resolve(updateResult)
        }
      } catch (error) {
        console.error('保存用户信息失败', error)
        reject(error)
      }
    })
  },
})
