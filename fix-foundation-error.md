# Foundation.onLoad 错误修复指南

## 错误描述
```
[渲染层错误] remote-debug start error TypeError: Foundation.onLoad is not a function
```

## 可能的原因和解决方案

### 1. 微信开发者工具缓存问题 (最常见)

**解决步骤**：
1. 在微信开发者工具中点击 **"清缓存"**
   - 工具栏 → 项目 → 清缓存 → 清除所有缓存
2. 重新编译项目
   - 快捷键：Ctrl+Shift+R (Windows) 或 Cmd+Shift+R (Mac)
3. 重启微信开发者工具

### 2. 云开发初始化问题

**检查步骤**：
1. 确认已开通云开发服务
2. 检查 `app.js` 中的云开发初始化代码
3. 确认云开发环境ID配置正确

**修复方法**：
```javascript
// 在 app.js 的 onLaunch 中添加错误处理
onLaunch() {
  try {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
      return
    }
    
    wx.cloud.init({
      // 如果有环境ID，请填入
      // env: 'your-env-id',
      traceUser: true,
    })
    
    console.log('云开发初始化成功')
  } catch (error) {
    console.error('云开发初始化失败', error)
  }
  
  // 其他初始化代码...
}
```

### 3. 基础库版本问题

**检查步骤**：
1. 在微信开发者工具中检查基础库版本
2. 确保使用 2.2.3 或以上版本

**修复方法**：
1. 点击右上角的 "详情"
2. 在 "本地设置" 中调整基础库版本
3. 选择 2.19.4 或更高版本

### 4. 页面路径配置问题

**检查 app.json 中的页面路径**：
```json
{
  "pages": [
    "pages/index/index",
    "pages/atlas/atlas",
    "pages/storage/storage",
    "pages/profile/profile",
    "pages/coffee-form/coffee-form",
    "pages/storage-form/storage-form"
  ]
}
```

确保所有页面文件都存在且路径正确。

### 5. 特定页面错误排查

**逐个测试页面**：
1. 临时注释掉 app.json 中的部分页面
2. 只保留 index 页面进行测试
3. 逐个添加页面，找出问题页面

**示例**：
```json
{
  "pages": [
    "pages/index/index"
    // 临时注释其他页面
    // "pages/atlas/atlas",
    // "pages/storage/storage",
    // "pages/profile/profile",
    // "pages/coffee-form/coffee-form",
    // "pages/storage-form/storage-form"
  ]
}
```

### 6. 代码语法检查

虽然我们已经检查过，但可以再次确认：

**检查要点**：
- 所有 Page() 函数是否正确闭合
- 所有方法是否有正确的逗号分隔
- 是否有未定义的变量或函数调用

### 7. 网络和权限问题

**检查步骤**：
1. 确认网络连接正常
2. 检查是否有网络请求被阻止
3. 确认云开发权限配置正确

## 推荐的修复顺序

1. **首先尝试**：清除缓存并重新编译
2. **如果仍有问题**：检查云开发配置
3. **进一步排查**：逐个测试页面
4. **最后手段**：重新创建项目并复制代码

## 预防措施

1. 定期清理开发者工具缓存
2. 保持基础库版本更新
3. 使用版本控制管理代码
4. 定期备份项目

## 如果问题仍然存在

请提供以下信息：
1. 微信开发者工具版本
2. 基础库版本
3. 具体的错误堆栈信息
4. 是否开通了云开发服务
5. 项目是否能在其他环境正常运行
