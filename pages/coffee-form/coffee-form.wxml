<!--pages/coffee-form/coffee-form.wxml-->
<view class="container">
  <view class="form-content">
    <view class="form-header">
      <view class="form-title">{{isEdit ? '编辑咖啡' : '添加咖啡'}}</view>
    </view>

    <scroll-view class="form-body" scroll-y>
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <view class="form-item">
          <view class="form-label">品牌 *</view>
          <input
            class="form-input"
            placeholder="请输入品牌名称"
            value="{{formData.brand}}"
            data-field="brand"
            bindinput="onInputChange"
          />
        </view>

        <view class="form-item">
          <view class="form-label">咖啡名称 *</view>
          <input
            class="form-input"
            placeholder="请输入咖啡名称"
            value="{{formData.name}}"
            data-field="name"
            bindinput="onInputChange"
          />
        </view>

        <view class="form-item">
          <view class="form-label">产地 *</view>
          <input
            class="form-input"
            placeholder="请输入产地"
            value="{{formData.origin}}"
            data-field="origin"
            bindinput="onInputChange"
          />
        </view>

        <view class="form-item">
          <view class="form-label">品种</view>
          <input
            class="form-input"
            placeholder="请输入咖啡品种"
            value="{{formData.variety}}"
            data-field="variety"
            bindinput="onInputChange"
          />
        </view>

        <view class="form-item">
          <view class="form-label">庄园</view>
          <input
            class="form-input"
            placeholder="请输入庄园名称"
            value="{{formData.estate}}"
            data-field="estate"
            bindinput="onInputChange"
          />
        </view>

        <view class="form-item">
          <view class="form-label">处理法</view>
          <input
            class="form-input"
            placeholder="请输入处理法"
            value="{{formData.process}}"
            data-field="process"
            bindinput="onInputChange"
          />
        </view>

        <view class="form-item">
          <view class="form-label">烘焙度</view>
          <picker
            mode="selector"
            range="{{roastLevels}}"
            value="{{roastLevels.indexOf(formData.roastLevel)}}"
            bindchange="onRoastLevelChange"
          >
            <view class="form-picker">
              {{formData.roastLevel || '请选择烘焙度'}}
            </view>
          </picker>
        </view>

        <view class="form-item">
          <view class="form-label">价格</view>
          <input
            class="form-input"
            type="digit"
            placeholder="请输入价格"
            value="{{formData.price}}"
            data-field="price"
            bindinput="onInputChange"
          />
        </view>
      </view>

      <!-- 风味描述 -->
      <view class="form-section">
        <view class="section-title">风味描述</view>

        <view class="form-item">
          <view class="form-label">风味</view>
          <textarea
            class="form-textarea"
            placeholder="请描述咖啡的风味特点"
            value="{{formData.flavor}}"
            data-field="flavor"
            bindinput="onInputChange"
            maxlength="200"
          />
        </view>
      </view>

      <!-- 个人评价 -->
      <view class="form-section">
        <view class="section-title">个人评价</view>

        <view class="form-item form-rate">
          <view class="form-label">评分</view>
          <view class="rating-input">
            <text
              wx:for="{{[1,2,3,4,5]}}"
              wx:for-index="starIndex"
              wx:key="*this"
              class="star {{formData.rating >= starIndex + 1 ? 'active' : ''}}"
              data-rating="{{starIndex + 1}}"
              bindtap="onRatingChange"
            >
              ⭐
            </text>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">评价</view>
          <textarea
            class="form-textarea"
            placeholder="请输入个人评价"
            value="{{formData.review}}"
            data-field="review"
            bindinput="onInputChange"
            maxlength="300"
          />
        </view>
      </view>

      <!-- 其他信息 -->
      <view class="form-section">
        <view class="section-title">其他信息</view>

        <view class="form-item">
          <view class="form-label">推荐研磨度</view>
          <input
            class="form-input"
            placeholder="请输入推荐研磨度"
            value="{{formData.grindSize}}"
            data-field="grindSize"
            bindinput="onInputChange"
          />
        </view>

        <view class="form-item">
          <view class="form-label">购买链接</view>
          <input
            class="form-input"
            placeholder="请输入购买链接"
            value="{{formData.purchaseLink}}"
            data-field="purchaseLink"
            bindinput="onInputChange"
          />
        </view>
      </view>
    </scroll-view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button class="btn btn-outline" bindtap="cancel">取消</button>
      <button class="btn" bindtap="saveCoffee" loading="{{loading}}">
        {{isEdit ? '更新' : '保存'}}
      </button>
    </view>
  </view>
</view>
