/**pages/profile/profile.wxss**/

/* 未登录状态 */
.login-section {
  padding: 120rpx 48rpx;
  text-align: center;
}

.login-prompt {
  background-color: var(--card-background);
  border-radius: 24rpx;
  padding: 80rpx 48rpx;
  box-shadow: var(--shadow);
}

.login-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.login-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.login-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 48rpx;
}

.login-btn {
  width: 100%;
  font-size: 32rpx;
  padding: 32rpx;
}

/* 已登录状态 */
.profile-content {
  padding: 32rpx;
}

/* 用户信息卡片 */
.user-card {
  display: flex;
  align-items: center;
  background-color: var(--card-background);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow);
  border: 1rpx solid var(--border-color);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  border: 4rpx solid var(--border-color);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 数据统计卡片 */
.stats-card {
  background-color: var(--card-background);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow);
  border: 1rpx solid var(--border-color);
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background-color: var(--background-color);
  border-radius: 16rpx;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 菜单部分 */
.menu-section {
  margin-bottom: 32rpx;
}

.menu-group {
  background-color: var(--card-background);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow);
  border: 1rpx solid var(--border-color);
  overflow: hidden;
}

.menu-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-secondary);
  padding: 24rpx 32rpx 16rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: var(--background-color);
}

.menu-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
  width: 48rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: var(--text-primary);
}

.menu-arrow {
  font-size: 28rpx;
  color: var(--text-light);
}

.logout-item {
  border-bottom: none;
}

.logout-item .menu-text {
  color: #ff4757;
}

.logout-item .menu-icon {
  opacity: 0.8;
}
