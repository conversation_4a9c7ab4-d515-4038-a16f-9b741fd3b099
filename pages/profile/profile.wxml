<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 未登录状态 -->
  <view wx:if="{{!isLoggedIn}}" class="login-section">
    <view class="login-prompt">
      <view class="login-icon">👤</view>
      <view class="login-title">请先登录</view>
      <view class="login-subtitle">登录后可查看个人数据和设置</view>
      <button class="btn login-btn" bindtap="handleLogin">
        立即登录
      </button>
    </view>
  </view>

  <!-- 已登录状态 -->
  <view wx:else class="profile-content">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
      <view class="user-info">
        <view class="user-name">{{userInfo.nickName}}</view>
        <view class="user-desc">咖啡爱好者</view>
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="stats-card">
      <view class="stats-title">我的数据</view>
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{stats.totalCoffees}}</view>
          <view class="stat-label">咖啡图谱</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{stats.totalStorage}}</view>
          <view class="stat-label">豆仓记录</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">¥{{stats.totalSpent}}</view>
          <view class="stat-label">总花费</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{stats.avgRating}}</view>
          <view class="stat-label">平均评分</view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-title">数据管理</view>
        <view class="menu-item" bindtap="exportData">
          <view class="menu-icon">📤</view>
          <view class="menu-text">导出数据</view>
          <view class="menu-arrow">></view>
        </view>
        <view class="menu-item" bindtap="backupData">
          <view class="menu-icon">☁️</view>
          <view class="menu-text">数据备份</view>
          <view class="menu-arrow">></view>
        </view>
        <view class="menu-item" bindtap="clearCache">
          <view class="menu-icon">🗑️</view>
          <view class="menu-text">清除缓存</view>
          <view class="menu-arrow">></view>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-title">帮助与支持</view>
        <view class="menu-item" bindtap="feedback">
          <view class="menu-icon">💬</view>
          <view class="menu-text">意见反馈</view>
          <view class="menu-arrow">></view>
        </view>
        <view class="menu-item" bindtap="aboutUs">
          <view class="menu-icon">ℹ️</view>
          <view class="menu-text">关于我们</view>
          <view class="menu-arrow">></view>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-item logout-item" bindtap="logout">
          <view class="menu-icon">🚪</view>
          <view class="menu-text">退出登录</view>
          <view class="menu-arrow">></view>
        </view>
      </view>
    </view>
  </view>
</view>
