// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    isLoggedIn: false,
    stats: {
      totalCoffees: 0,
      totalStorage: 0,
      totalSpent: 0,
      avgRating: 0
    }
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    this.checkLoginStatus()
    if (this.data.isLoggedIn) {
      this.loadUserStats()
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = app.globalData.userInfo
    const isLoggedIn = app.globalData.isLoggedIn
    
    this.setData({
      userInfo,
      isLoggedIn
    })
  },

  // 用户登录
  async handleLogin() {
    try {
      await app.login()
      const userInfo = await app.getUserInfo()
      
      this.setData({
        userInfo,
        isLoggedIn: true
      })
      
      this.loadUserStats()
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('登录失败', error)
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      const db = wx.cloud.database()
      
      // 获取咖啡图谱数据
      const coffeeAtlas = await db.collection('coffee_atlas').where({
        _openid: app.globalData.openid
      }).get()
      
      // 获取豆仓数据
      const coffeeStorage = await db.collection('coffee_storage').where({
        _openid: app.globalData.openid
      }).get()
      
      // 计算统计数据
      const totalCoffees = coffeeAtlas.data.length
      const totalStorage = coffeeStorage.data.length
      
      // 计算总花费
      let totalSpent = 0
      coffeeStorage.data.forEach(item => {
        if (item.price) {
          totalSpent += parseFloat(item.price)
        }
      })
      
      // 计算平均评分
      let totalRating = 0
      let ratedCount = 0
      coffeeAtlas.data.forEach(item => {
        if (item.rating) {
          totalRating += item.rating
          ratedCount++
        }
      })
      const avgRating = ratedCount > 0 ? (totalRating / ratedCount).toFixed(1) : 0
      
      this.setData({
        stats: {
          totalCoffees,
          totalStorage,
          totalSpent: totalSpent.toFixed(2),
          avgRating
        }
      })
      
    } catch (error) {
      console.error('加载统计数据失败', error)
    }
  },

  // 导出数据
  exportData() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    })
  },

  // 数据备份
  backupData() {
    wx.showToast({
      title: '备份功能开发中',
      icon: 'none'
    })
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除本地缓存吗？',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorageSync()
          wx.showToast({
            title: '缓存已清除',
            icon: 'success'
          })
        }
      }
    })
  },

  // 关于我们
  aboutUs() {
    wx.showModal({
      title: '关于我们',
      content: '我的咖啡 v1.0.0\\n\\n专业的咖啡豆管理工具，帮助咖啡爱好者记录和管理自己的咖啡收藏。',
      showCancel: false
    })
  },

  // 意见反馈
  feedback() {
    wx.showToast({
      title: '反馈功能开发中',
      icon: 'none'
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户信息
          wx.removeStorageSync('userInfo')
          app.globalData.userInfo = null
          app.globalData.isLoggedIn = false
          
          this.setData({
            userInfo: null,
            isLoggedIn: false,
            stats: {
              totalCoffees: 0,
              totalStorage: 0,
              totalSpent: 0,
              avgRating: 0
            }
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  }
})
