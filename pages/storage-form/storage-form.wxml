<!--pages/storage-form/storage-form.wxml-->
<view class="container">
  <view class="form-content">
    <view class="form-header">
      <view class="form-title">{{isEdit ? '编辑豆仓记录' : '添加豆仓记录'}}</view>
    </view>

    <scroll-view class="form-body" scroll-y>
      <!-- 咖啡信息 -->
      <view class="form-section">
        <view class="section-title">咖啡信息</view>

        <view class="form-item">
          <view class="form-label">选择咖啡</view>
          <view class="coffee-selector" bindtap="showCoffeeSelector">
            <view class="selector-content">
              <view wx:if="{{formData.coffeeName}}" class="selected-coffee">
                <view class="coffee-name">{{formData.coffeeName}}</view>
                <view class="coffee-brand">{{formData.brand}}</view>
              </view>
              <view wx:else class="placeholder">点击选择咖啡</view>
            </view>
            <view class="selector-arrow">&gt;</view>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">咖啡名称 *</view>
          <input
            class="form-input"
            placeholder="请输入咖啡名称"
            value="{{formData.coffeeName}}"
            data-field="coffeeName"
            bindinput="onInputChange"
          />
        </view>

        <view class="form-item">
          <view class="form-label">品牌 *</view>
          <input
            class="form-input"
            placeholder="请输入品牌"
            value="{{formData.brand}}"
            data-field="brand"
            bindinput="onInputChange"
          />
        </view>
      </view>

      <!-- 购买信息 -->
      <view class="form-section">
        <view class="section-title">购买信息</view>

        <view class="form-item">
          <view class="form-label">购买日期 *</view>
          <picker
            mode="date"
            value="{{formData.purchaseDate}}"
            data-field="purchaseDate"
            bindchange="onDateChange"
          >
            <view class="form-picker">
              {{formData.purchaseDate || '请选择购买日期'}}
            </view>
          </picker>
        </view>

        <view class="form-item">
          <view class="form-label">重量 (g)</view>
          <input
            class="form-input"
            type="digit"
            placeholder="请输入重量"
            value="{{formData.weight}}"
            data-field="weight"
            bindinput="onInputChange"
          />
        </view>

        <view class="form-item">
          <view class="form-label">价格</view>
          <input
            class="form-input"
            type="digit"
            placeholder="请输入价格"
            value="{{formData.price}}"
            data-field="price"
            bindinput="onInputChange"
          />
        </view>
      </view>

      <!-- 状态信息 -->
      <view class="form-section">
        <view class="section-title">状态信息</view>

        <view class="form-item">
          <view class="form-label">状态</view>
          <picker
            mode="selector"
            range="{{statusOptions}}"
            range-key="label"
            value="{{currentStatusIndex}}"
            bindchange="onStatusChange"
          >
            <view class="form-picker">
              {{currentStatusLabel}}
            </view>
          </picker>
        </view>

        <view wx:if="{{formData.status === 'opened' || formData.status === 'finished'}}" class="form-item">
          <view class="form-label">开封日期</view>
          <picker
            mode="date"
            value="{{formData.openDate}}"
            data-field="openDate"
            bindchange="onDateChange"
          >
            <view class="form-picker">
              {{formData.openDate || '请选择开封日期'}}
            </view>
          </picker>
        </view>

        <view wx:if="{{formData.status === 'finished'}}" class="form-item">
          <view class="form-label">饮尽日期</view>
          <picker
            mode="date"
            value="{{formData.finishDate}}"
            data-field="finishDate"
            bindchange="onDateChange"
          >
            <view class="form-picker">
              {{formData.finishDate || '请选择饮尽日期'}}
            </view>
          </picker>
        </view>
      </view>

      <!-- 备注 -->
      <view class="form-section">
        <view class="section-title">备注</view>

        <view class="form-item">
          <view class="form-label">备注</view>
          <textarea
            class="form-textarea"
            placeholder="请输入备注信息"
            value="{{formData.notes}}"
            data-field="notes"
            bindinput="onInputChange"
            maxlength="200"
          />
        </view>
      </view>
    </scroll-view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button class="btn btn-outline" bindtap="cancel">取消</button>
      <button class="btn" bindtap="saveStorage" loading="{{loading}}">
        {{isEdit ? '更新' : '保存'}}
      </button>
    </view>
  </view>

  <!-- 咖啡选择器弹窗 -->
  <view wx:if="{{showCoffeeSelector}}" class="selector-modal" bindtap="hideCoffeeSelector">
    <view class="selector-content-modal" catchtap="">
      <view class="selector-header">
        <view class="selector-title">选择咖啡</view>
        <view class="selector-close" bindtap="hideCoffeeSelector">✕</view>
      </view>

      <scroll-view class="selector-list" scroll-y>
        <view wx:if="{{coffeeList.length === 0}}" class="empty-state">
          <view class="empty-text">还没有咖啡记录</view>
          <view class="empty-hint">请先在咖啡图谱中添加咖啡</view>
        </view>

        <view wx:else>
          <view
            wx:for="{{coffeeList}}"
            wx:key="_id"
            class="coffee-option"
            data-coffee="{{item}}"
            bindtap="selectCoffee"
          >
            <view class="option-name">{{item.name}}</view>
            <view class="option-brand">{{item.brand}}</view>
            <view class="option-origin">{{item.origin}}</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
