/**pages/storage-form/storage-form.wxss**/

.form-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.form-header {
  padding: 32rpx;
  background-color: var(--card-background);
  border-bottom: 1rpx solid var(--border-color);
}

.form-title {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
}

.form-body {
  flex: 1;
  padding: 32rpx;
}

.form-section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: calc(100% - 64rpx);
  height: 88rpx;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  font-size: 32rpx;
  line-height: 40rpx;
  color: var(--text-primary);
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: var(--primary-color);
}

.form-picker {
  width: calc(100% - 64rpx);
  height: 88rpx;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  font-size: 32rpx;
  line-height: 40rpx;
  color: var(--text-primary);
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.form-picker::after {
  content: '>';
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
  color: var(--text-light);
  font-size: 24rpx;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  font-size: 32rpx;
  color: var(--text-primary);
  transition: border-color 0.3s ease;
}

.form-textarea:focus {
  border-color: var(--primary-color);
}

/* 咖啡选择器 */
.coffee-selector {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  transition: border-color 0.3s ease;
}

.coffee-selector:active {
  border-color: var(--primary-color);
}

.selector-content {
  flex: 1;
}

.selected-coffee .coffee-name {
  font-size: 32rpx;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.selected-coffee .coffee-brand {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.placeholder {
  font-size: 32rpx;
  color: var(--text-light);
}

.selector-arrow {
  font-size: 24rpx;
  color: var(--text-light);
  transform: rotate(90deg);
}

.form-actions {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  background-color: var(--card-background);
  border-top: 1rpx solid var(--border-color);
}

.form-actions .btn {
  flex: 1;
  font-size: 32rpx;
  padding: 32rpx;
}

/* 咖啡选择器弹窗 */
.selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.selector-content-modal {
  background-color: var(--card-background);
  border-radius: 24rpx;
  width: 90%;
  max-height: 70%;
  display: flex;
  flex-direction: column;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.selector-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.selector-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-light);
}

.selector-list {
  flex: 1;
  padding: 24rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.empty-hint {
  font-size: 28rpx;
  color: var(--text-light);
}

.coffee-option {
  padding: 32rpx;
  background-color: var(--background-color);
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.coffee-option:active {
  background-color: var(--primary-color);
  transform: scale(0.98);
}

.coffee-option:active .option-name,
.coffee-option:active .option-brand,
.coffee-option:active .option-origin {
  color: white;
}

.option-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.option-brand {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 4rpx;
}

.option-origin {
  font-size: 26rpx;
  color: var(--text-light);
}
