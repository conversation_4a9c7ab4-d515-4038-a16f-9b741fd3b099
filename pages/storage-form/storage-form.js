// pages/storage-form/storage-form.js
const app = getApp()

Page({
  data: {
    isEdit: false,
    storageId: '',
    formData: {
      coffeeId: '',
      coffeeName: '',
      brand: '',
      purchaseDate: '',
      openDate: '',
      finishDate: '',
      weight: '',
      price: '',
      status: 'unopened',
      notes: ''
    },
    statusOptions: [
      { value: 'unopened', label: '未开封' },
      { value: 'opened', label: '已开封' },
      { value: 'finished', label: '已饮尽' }
    ],
    coffeeList: [],
    showCoffeeSelector: false,
    loading: false,
    currentStatusIndex: 0,
    currentStatusLabel: '未开封'
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        isEdit: true,
        storageId: options.id
      })
      this.loadStorageData(options.id)
    }

    this.loadCoffeeList()
    this.updateStatusDisplay()
  },

  // 加载豆仓数据（编辑模式）
  async loadStorageData(storageId) {
    try {
      const db = wx.cloud.database()
      const result = await db.collection('coffee_storage').doc(storageId).get()

      if (result.data) {
        const data = result.data
        // 格式化日期
        if (data.purchaseDate) {
          data.purchaseDate = this.formatDateForInput(data.purchaseDate)
        }
        if (data.openDate) {
          data.openDate = this.formatDateForInput(data.openDate)
        }
        if (data.finishDate) {
          data.finishDate = this.formatDateForInput(data.finishDate)
        }

        this.setData({
          formData: data
        })
        this.updateStatusDisplay()
      }
    } catch (error) {
      console.error('加载豆仓数据失败', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 加载咖啡列表
  async loadCoffeeList() {
    try {
      const db = wx.cloud.database()
      const result = await db.collection('coffee_atlas')
        .where({
          _openid: app.globalData.openid
        })
        .orderBy('createTime', 'desc')
        .get()

      this.setData({
        coffeeList: result.data
      })
    } catch (error) {
      console.error('加载咖啡列表失败', error)
    }
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value

    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 日期变化
  onDateChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value

    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 状态变化
  onStatusChange(e) {
    const index = e.detail.value
    const selectedOption = this.data.statusOptions[index]
    this.setData({
      'formData.status': selectedOption.value,
      currentStatusIndex: index,
      currentStatusLabel: selectedOption.label
    })
  },

  // 更新状态显示
  updateStatusDisplay() {
    const currentStatus = this.data.formData.status
    const index = this.data.statusOptions.findIndex(item => item.value === currentStatus)
    const label = this.data.statusOptions.find(item => item.value === currentStatus)?.label || '未开封'

    this.setData({
      currentStatusIndex: index >= 0 ? index : 0,
      currentStatusLabel: label
    })
  },

  // 显示咖啡选择器
  showCoffeeSelector() {
    this.setData({ showCoffeeSelector: true })
  },

  // 隐藏咖啡选择器
  hideCoffeeSelector() {
    this.setData({ showCoffeeSelector: false })
  },

  // 选择咖啡
  selectCoffee(e) {
    const coffee = e.currentTarget.dataset.coffee
    this.setData({
      'formData.coffeeId': coffee._id,
      'formData.coffeeName': coffee.name,
      'formData.brand': coffee.brand,
      showCoffeeSelector: false
    })
  },

  // 格式化日期为输入框格式
  formatDateForInput(date) {
    if (!date) return ''
    const d = new Date(date)
    const year = d.getFullYear()
    const month = (d.getMonth() + 1).toString().padStart(2, '0')
    const day = d.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 表单验证
  validateForm() {
    const { coffeeName, brand, purchaseDate } = this.data.formData

    if (!coffeeName.trim()) {
      wx.showToast({
        title: '请输入咖啡名称',
        icon: 'none'
      })
      return false
    }

    if (!brand.trim()) {
      wx.showToast({
        title: '请输入品牌',
        icon: 'none'
      })
      return false
    }

    if (!purchaseDate) {
      wx.showToast({
        title: '请选择购买日期',
        icon: 'none'
      })
      return false
    }

    return true
  },

  // 保存豆仓记录
  async saveStorage() {
    if (!this.validateForm()) {
      return
    }

    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const db = wx.cloud.database()
      const formData = { ...this.data.formData }

      // 转换数字字段
      if (formData.weight) {
        formData.weight = parseFloat(formData.weight)
      }
      if (formData.price) {
        formData.price = parseFloat(formData.price)
      }

      // 转换日期字段
      if (formData.purchaseDate) {
        formData.purchaseDate = new Date(formData.purchaseDate)
      }
      if (formData.openDate) {
        formData.openDate = new Date(formData.openDate)
      }
      if (formData.finishDate) {
        formData.finishDate = new Date(formData.finishDate)
      }

      if (this.data.isEdit) {
        // 更新现有记录
        await db.collection('coffee_storage').doc(this.data.storageId).update({
          data: {
            ...formData,
            updateTime: new Date()
          }
        })

        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
      } else {
        // 创建新记录
        await db.collection('coffee_storage').add({
          data: {
            ...formData,
            createTime: new Date(),
            updateTime: new Date()
          }
        })

        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
      }

      this.setData({ loading: false })

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('保存失败', error)
      this.setData({ loading: false })

      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  // 取消操作
  cancel() {
    wx.navigateBack()
  }
})
