/**pages/add-coffee/add-coffee.wxss**/


.container {
  width: 740rpx;
  background-color: var(--background-color);
} 

.form-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background-color: var(--card-background);
  border-bottom: 1rpx solid var(--border-color);
}

.header-left {
  display: flex;
  align-items: center;
  min-width: 120rpx;
}

.back-icon {
  font-size: 48rpx;
  color: var(--primary-color);
  margin-right: 8rpx;
  font-weight: bold;
}

.back-text {
  font-size: 32rpx;
  color: var(--primary-color);
}

.form-title {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  flex: 1;
}

.header-right {
  min-width: 120rpx;
}

.form-body {
  flex: 1;
  padding: 32rpx;
  padding-bottom: 120rpx;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.form-section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.form-item {
  display: flex;
  width: 690rpx;
  align-items: center;
  margin-bottom: 32rpx;
  /* margin-right: 48rpx; */
  gap: 24rpx;
}

.form-item-vertical {
  flex-direction: column;
  align-items: flex-start;
}

.form-item-vertical .form-label {
  margin-bottom: 16rpx;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 140rpx;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  height: 88rpx;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  font-size: 32rpx;
  line-height: 40rpx;
  color: var(--text-primary);
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: var(--primary-color);
}

.form-picker {
  flex: 1;
  height: 88rpx;
  width: 530rpx;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  font-size: 32rpx;
  line-height: 40rpx;
  color: var(--text-primary);
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.form-picker::after {
  content: '>';
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
  color: var(--text-light);
  font-size: 24rpx;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  font-size: 32rpx;
  color: var(--text-primary);
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: var(--primary-color);
}

.rating-container {
  margin-bottom: 32rpx;
}

.rating-input {
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: flex-start;
}

.star {
  font-size: 48rpx;
  opacity: 0.3;
  transition: opacity 0.3s ease;
  line-height: 0.8;
  display: inline-block;
  vertical-align: middle;
}

.star.active {
  opacity: 1;
}

.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  background-color: var(--card-background);
  border-top: 1rpx solid var(--border-color);
  z-index: 100;
  max-width: 750rpx;
  margin: 0 auto;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.form-actions .btn {
  flex: 1;
  font-size: 32rpx;
  padding: 32rpx;
}
