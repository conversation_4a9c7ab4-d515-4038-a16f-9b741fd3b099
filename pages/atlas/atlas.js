// pages/atlas/atlas.js
const app = getApp()

Page({
  data: {
    coffeeList: [],
    loading: false,
    searchKeyword: '',
    showSearch: false,
    filteredList: [],
    selectedCoffee: null,
    showDetail: false
  },

  onLoad() {
    this.loadCoffeeAtlas()
  },

  onShow() {
    this.loadCoffeeAtlas()
  },

  // 加载咖啡图谱
  async loadCoffeeAtlas() {
    if (!app.globalData.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    try {
      const db = wx.cloud.database()
      const result = await db.collection('coffee_atlas')
        .where({
          _openid: app.globalData.openid
        })
        .orderBy('createTime', 'desc')
        .get()

      this.setData({
        coffeeList: result.data,
        filteredList: result.data,
        loading: false
      })

    } catch (error) {
      console.error('加载咖啡图谱失败', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 搜索功能
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })
    this.filterCoffeeList(keyword)
  },

  // 过滤咖啡列表
  filterCoffeeList(keyword) {
    if (!keyword) {
      this.setData({ filteredList: this.data.coffeeList })
      return
    }

    const filtered = this.data.coffeeList.filter(coffee => {
      return coffee.brand.includes(keyword) ||
             coffee.name.includes(keyword) ||
             coffee.origin.includes(keyword) ||
             coffee.variety.includes(keyword) ||
             coffee.flavor.includes(keyword)
    })

    this.setData({ filteredList: filtered })
  },

  // 切换搜索显示
  toggleSearch() {
    this.setData({ 
      showSearch: !this.data.showSearch,
      searchKeyword: '',
      filteredList: this.data.coffeeList
    })
  },

  // 添加新咖啡
  addCoffee() {
    wx.navigateTo({
      url: '/pages/add-coffee/add-coffee'
    })
  },

  // 查看咖啡详情
  viewCoffeeDetail(e) {
    const coffee = e.currentTarget.dataset.coffee
    this.setData({
      selectedCoffee: coffee,
      showDetail: true
    })
  },

  // 关闭详情
  closeDetail() {
    this.setData({
      selectedCoffee: null,
      showDetail: false
    })
  },

  // 编辑咖啡
  editCoffee() {
    const coffeeId = this.data.selectedCoffee._id
    wx.navigateTo({
      url: `/pages/add-coffee/add-coffee?id=${coffeeId}`
    })
  },

  // 删除咖啡
  async deleteCoffee() {
    const that = this
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个咖啡记录吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const db = wx.cloud.database()
            await db.collection('coffee_atlas').doc(that.data.selectedCoffee._id).remove()
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            
            that.closeDetail()
            that.loadCoffeeAtlas()
            
          } catch (error) {
            console.error('删除失败', error)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 添加到豆仓
  async addToStorage() {
    const coffee = this.data.selectedCoffee
    
    try {
      const db = wx.cloud.database()
      await db.collection('coffee_storage').add({
        data: {
          coffeeId: coffee._id,
          coffeeName: coffee.name,
          brand: coffee.brand,
          purchaseDate: new Date(),
          status: 'unopened', // unopened, opened, finished
          createTime: new Date()
        }
      })
      
      wx.showToast({
        title: '已添加到豆仓',
        icon: 'success'
      })
      
      this.closeDetail()
      
    } catch (error) {
      console.error('添加到豆仓失败', error)
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      })
    }
  },

  // 分享咖啡
  shareCoffee() {
    // TODO: 实现分享功能
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    })
  }
})
