<!--pages/atlas/atlas.wxml-->
<view class="container">
  <!-- 头部操作栏 -->
  <view class="header">
    <view class="header-title">咖啡图谱</view>
    <view class="header-actions">
      <view class="action-btn" bindtap="toggleSearch">
        <text class="icon">🔍</text>
      </view>
      <view class="action-btn" bindtap="addCoffee">
        <text class="icon">➕</text>
      </view>
    </view>
  </view>

  <!-- 搜索框 -->
  <view wx:if="{{showSearch}}" class="search-section">
    <input
      class="search-input"
      placeholder="搜索品牌、名称、产地、品种、风味..."
      value="{{searchKeyword}}"
      bindinput="onSearchInput"
    />
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 咖啡列表 -->
  <view wx:else class="coffee-list">
    <view wx:if="{{filteredList.length === 0}}" class="empty-state">
      <view class="empty-state-icon">☕</view>
      <view class="empty-state-text">
        {{searchKeyword ? '没有找到相关咖啡' : '还没有添加咖啡记录'}}
      </view>
      <button wx:if="{{!searchKeyword}}" class="btn" bindtap="addCoffee">
        添加第一个咖啡
      </button>
    </view>

    <view wx:else>
      <view
        wx:for="{{filteredList}}"
        wx:key="_id"
        class="coffee-item"
        data-coffee="{{item}}"
        bindtap="viewCoffeeDetail"
      >
        <view class="coffee-header">
          <view class="coffee-brand">{{item.brand}}</view>
          <view class="coffee-price">¥{{item.price}}</view>
        </view>
        <view class="coffee-name">{{item.name}}</view>
        <view class="coffee-info">
          <text class="info-item">{{item.origin}}</text>
          <text class="info-item">{{item.variety}}</text>
          <text class="info-item">{{item.roastLevel}}</text>
        </view>
        <view class="coffee-flavor">{{item.flavor}}</view>
        <view class="coffee-rating">
          <text wx:for="{{[1,2,3,4,5]}}" wx:for-index="starIndex" wx:key="*this" class="star {{item.rating >= starIndex + 1 ? 'active' : ''}}">⭐</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 咖啡详情弹窗 -->
  <view wx:if="{{showDetail}}" class="detail-modal" bindtap="closeDetail">
    <view class="detail-content" catchtap="">
      <view class="detail-header">
        <view class="detail-title">{{selectedCoffee.name}}</view>
        <view class="detail-close" bindtap="closeDetail">✕</view>
      </view>

      <scroll-view class="detail-body" scroll-y>
        <view class="detail-section">
          <view class="section-title">基本信息</view>
          <view class="info-row">
            <text class="label">品牌：</text>
            <text class="value">{{selectedCoffee.brand}}</text>
          </view>
          <view class="info-row">
            <text class="label">产地：</text>
            <text class="value">{{selectedCoffee.origin}}</text>
          </view>
          <view class="info-row">
            <text class="label">品种：</text>
            <text class="value">{{selectedCoffee.variety}}</text>
          </view>
          <view class="info-row">
            <text class="label">处理法：</text>
            <text class="value">{{selectedCoffee.process}}</text>
          </view>
          <view class="info-row">
            <text class="label">烘焙度：</text>
            <text class="value">{{selectedCoffee.roastLevel}}</text>
          </view>
          <view class="info-row">
            <text class="label">价格：</text>
            <text class="value">¥{{selectedCoffee.price}}</text>
          </view>
        </view>

        <view class="detail-section">
          <view class="section-title">风味描述</view>
          <view class="flavor-text">{{selectedCoffee.flavor}}</view>
        </view>

        <view class="detail-section">
          <view class="section-title">个人评价</view>
          <view class="rating-display">
            <text wx:for="{{[1,2,3,4,5]}}" wx:for-index="starIndex" wx:key="*this" class="star {{selectedCoffee.rating >= starIndex + 1 ? 'active' : ''}}">⭐</text>
          </view>
          <view class="review-text">{{selectedCoffee.review}}</view>
        </view>

        <view wx:if="{{selectedCoffee.grindSize}}" class="detail-section">
          <view class="section-title">推荐研磨度</view>
          <view class="grind-text">{{selectedCoffee.grindSize}}</view>
        </view>

        <view wx:if="{{selectedCoffee.purchaseLink}}" class="detail-section">
          <view class="section-title">购买链接</view>
          <view class="link-text">{{selectedCoffee.purchaseLink}}</view>
        </view>
      </scroll-view>

      <view class="detail-actions">
        <button class="btn btn-outline" bindtap="addToStorage">加到豆仓</button>
        <button class="btn btn-outline" bindtap="editCoffee">编辑</button>
        <button class="btn btn-outline" bindtap="shareCoffee">分享</button>
        <button class="btn btn-outline delete-btn" bindtap="deleteCoffee">删除</button>
      </view>
    </view>
  </view>
</view>
