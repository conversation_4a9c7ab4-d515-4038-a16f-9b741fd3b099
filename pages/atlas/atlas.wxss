/**pages/atlas/atlas.wxss**/

/* 头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background-color: var(--card-background);
  border-bottom: 1rpx solid var(--border-color);
}

.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 72rpx;
  height: 72rpx;
  background-color: var(--background-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.action-btn:active {
  background-color: var(--primary-color);
}

.action-btn:active .icon {
  color: white;
}

.icon {
  font-size: 32rpx;
}

/* 搜索框 */
.search-section {
  padding: 24rpx 32rpx;
  background-color: var(--card-background);
  border-bottom: 1rpx solid var(--border-color);
}

.search-input {
  width: 100%;
  padding: 24rpx 32rpx;
  background-color: var(--background-color);
  border: 2rpx solid var(--border-color);
  border-radius: 48rpx;
  font-size: 28rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 120rpx 32rpx;
  color: var(--text-light);
  font-size: 28rpx;
}

/* 咖啡列表 */
.coffee-list {
  padding: 24rpx;
}

.coffee-item {
  background-color: var(--card-background);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow);
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.coffee-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 6rpx rgba(139, 69, 19, 0.15);
}

.coffee-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.coffee-brand {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.coffee-price {
  font-size: 32rpx;
  color: var(--primary-color);
  font-weight: 600;
}

.coffee-name {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.coffee-info {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.info-item {
  background-color: var(--accent-color);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.coffee-flavor {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.coffee-rating {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 24rpx;
  opacity: 0.3;
}

.star.active {
  opacity: 1;
}

/* 详情弹窗 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-content {
  background-color: var(--card-background);
  border-radius: 24rpx;
  width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.detail-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.detail-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-light);
}

.detail-body {
  flex: 1;
  padding: 32rpx;
}

.detail-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
}

.label {
  font-size: 28rpx;
  color: var(--text-secondary);
  width: 120rpx;
}

.value {
  font-size: 28rpx;
  color: var(--text-primary);
  flex: 1;
}

.flavor-text,
.review-text,
.grind-text,
.link-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

.rating-display {
  display: flex;
  gap: 4rpx;
  margin-bottom: 16rpx;
}

.detail-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid var(--border-color);
}

.detail-actions .btn {
  flex: 1;
  font-size: 28rpx;
  padding: 20rpx 16rpx;
}

.delete-btn {
  color: #ff4757 !important;
  border-color: #ff4757 !important;
}

.delete-btn:active {
  background-color: #ff4757 !important;
  color: white !important;
}
