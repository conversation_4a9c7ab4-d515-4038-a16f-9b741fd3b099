// pages/storage/storage.js
const app = getApp()
const util = require('../../utils/util.js')

Page({
  data: {
    storageList: [],
    loading: false,
    filterStatus: 'all', // all, unopened, opened, finished
    statusOptions: [
      { value: 'all', label: '全部' },
      { value: 'unopened', label: '未开封' },
      { value: 'opened', label: '已开封' },
      { value: 'finished', label: '已饮尽' }
    ],
    selectedStorage: null,
    showDetail: false
  },

  onLoad() {
    this.loadStorageList()
  },

  onShow() {
    this.loadStorageList()
  },

  // 加载豆仓列表
  async loadStorageList() {
    if (!app.globalData.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    try {
      const db = wx.cloud.database()
      let query = db.collection('coffee_storage')
        .where({
          _openid: app.globalData.openid
        })

      // 根据状态过滤
      if (this.data.filterStatus !== 'all') {
        query = query.where({
          status: this.data.filterStatus
        })
      }

      const result = await query
        .orderBy('createTime', 'desc')
        .get()

      this.setData({
        storageList: result.data,
        loading: false
      })

    } catch (error) {
      console.error('加载豆仓列表失败', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 切换状态过滤
  onStatusChange(e) {
    const status = e.currentTarget.dataset.status
    this.setData({ filterStatus: status })
    this.loadStorageList()
  },

  // 添加新的豆仓记录
  addStorage() {
    wx.navigateTo({
      url: '/pages/storage-form/storage-form'
    })
  },

  // 查看豆仓详情
  viewStorageDetail(e) {
    const storage = e.currentTarget.dataset.storage
    this.setData({
      selectedStorage: storage,
      showDetail: true
    })
  },

  // 关闭详情
  closeDetail() {
    this.setData({
      selectedStorage: null,
      showDetail: false
    })
  },

  // 编辑豆仓记录
  editStorage() {
    const storageId = this.data.selectedStorage._id
    wx.navigateTo({
      url: `/pages/storage-form/storage-form?id=${storageId}`
    })
  },

  // 更新状态
  async updateStatus(e) {
    const newStatus = e.currentTarget.dataset.status
    const storage = this.data.selectedStorage

    try {
      const db = wx.cloud.database()
      const updateData = { status: newStatus }

      // 根据状态设置时间
      if (newStatus === 'opened' && !storage.openDate) {
        updateData.openDate = new Date()
      } else if (newStatus === 'finished' && !storage.finishDate) {
        updateData.finishDate = new Date()
      }

      await db.collection('coffee_storage').doc(storage._id).update({
        data: updateData
      })

      wx.showToast({
        title: '状态更新成功',
        icon: 'success'
      })

      this.closeDetail()
      this.loadStorageList()

    } catch (error) {
      console.error('更新状态失败', error)
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      })
    }
  },

  // 删除豆仓记录
  async deleteStorage() {
    const that = this
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个豆仓记录吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const db = wx.cloud.database()
            await db.collection('coffee_storage').doc(that.data.selectedStorage._id).remove()

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })

            that.closeDetail()
            that.loadStorageList()

          } catch (error) {
            console.error('删除失败', error)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 格式化日期
  formatDate(date) {
    if (!date) return '未设置'
    return util.formatDate(date)
  },

  // 获取状态文本
  getStatusText(status) {
    return util.getStatusText(status)
  },

  // 获取状态颜色
  getStatusColor(status) {
    return util.getStatusColor(status)
  }
})
