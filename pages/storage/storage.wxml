<!--pages/storage/storage.wxml-->
<view class="container">
  <!-- 头部操作栏 -->
  <view class="header">
    <view class="header-title">我的豆仓</view>
    <view class="header-actions">
      <view class="action-btn" bindtap="addStorage">
        <text class="icon">➕</text>
      </view>
    </view>
  </view>

  <!-- 状态过滤 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x>
      <view class="filter-list">
        <view 
          wx:for="{{statusOptions}}" 
          wx:key="value" 
          class="filter-item {{filterStatus === item.value ? 'active' : ''}}"
          data-status="{{item.value}}"
          bindtap="onStatusChange"
        >
          {{item.label}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 豆仓列表 -->
  <view wx:else class="storage-list">
    <view wx:if="{{storageList.length === 0}}" class="empty-state">
      <view class="empty-state-icon">🏭</view>
      <view class="empty-state-text">还没有豆仓记录</view>
      <button class="btn" bindtap="addStorage">
        添加第一个记录
      </button>
    </view>

    <view wx:else>
      <view 
        wx:for="{{storageList}}" 
        wx:key="_id" 
        class="storage-item"
        data-storage="{{item}}"
        bindtap="viewStorageDetail"
      >
        <view class="storage-header">
          <view class="storage-name">{{item.coffeeName}}</view>
          <view class="storage-status" style="background-color: {{getStatusColor(item.status)}}">
            {{getStatusText(item.status)}}
          </view>
        </view>
        <view class="storage-brand">{{item.brand}}</view>
        <view class="storage-dates">
          <view class="date-item">
            <text class="date-label">购买：</text>
            <text class="date-value">{{formatDate(item.purchaseDate)}}</text>
          </view>
          <view wx:if="{{item.openDate}}" class="date-item">
            <text class="date-label">开封：</text>
            <text class="date-value">{{formatDate(item.openDate)}}</text>
          </view>
          <view wx:if="{{item.finishDate}}" class="date-item">
            <text class="date-label">饮尽：</text>
            <text class="date-value">{{formatDate(item.finishDate)}}</text>
          </view>
        </view>
        <view wx:if="{{item.notes}}" class="storage-notes">{{item.notes}}</view>
      </view>
    </view>
  </view>

  <!-- 豆仓详情弹窗 -->
  <view wx:if="{{showDetail}}" class="detail-modal" bindtap="closeDetail">
    <view class="detail-content" catchtap="">
      <view class="detail-header">
        <view class="detail-title">{{selectedStorage.coffeeName}}</view>
        <view class="detail-close" bindtap="closeDetail">✕</view>
      </view>
      
      <scroll-view class="detail-body" scroll-y>
        <view class="detail-section">
          <view class="section-title">基本信息</view>
          <view class="info-row">
            <text class="label">品牌：</text>
            <text class="value">{{selectedStorage.brand}}</text>
          </view>
          <view class="info-row">
            <text class="label">状态：</text>
            <text class="value status-text" style="color: {{getStatusColor(selectedStorage.status)}}">
              {{getStatusText(selectedStorage.status)}}
            </text>
          </view>
          <view class="info-row">
            <text class="label">购买日期：</text>
            <text class="value">{{formatDate(selectedStorage.purchaseDate)}}</text>
          </view>
          <view wx:if="{{selectedStorage.openDate}}" class="info-row">
            <text class="label">开封日期：</text>
            <text class="value">{{formatDate(selectedStorage.openDate)}}</text>
          </view>
          <view wx:if="{{selectedStorage.finishDate}}" class="info-row">
            <text class="label">饮尽日期：</text>
            <text class="value">{{formatDate(selectedStorage.finishDate)}}</text>
          </view>
          <view wx:if="{{selectedStorage.weight}}" class="info-row">
            <text class="label">重量：</text>
            <text class="value">{{selectedStorage.weight}}g</text>
          </view>
          <view wx:if="{{selectedStorage.price}}" class="info-row">
            <text class="label">价格：</text>
            <text class="value">¥{{selectedStorage.price}}</text>
          </view>
        </view>

        <view wx:if="{{selectedStorage.notes}}" class="detail-section">
          <view class="section-title">备注</view>
          <view class="notes-text">{{selectedStorage.notes}}</view>
        </view>

        <!-- 状态操作 -->
        <view class="detail-section">
          <view class="section-title">状态操作</view>
          <view class="status-actions">
            <button 
              wx:if="{{selectedStorage.status === 'unopened'}}"
              class="btn btn-outline"
              data-status="opened"
              bindtap="updateStatus"
            >
              标记为已开封
            </button>
            <button 
              wx:if="{{selectedStorage.status === 'opened'}}"
              class="btn btn-outline"
              data-status="finished"
              bindtap="updateStatus"
            >
              标记为已饮尽
            </button>
            <button 
              wx:if="{{selectedStorage.status === 'finished'}}"
              class="btn btn-outline"
              data-status="unopened"
              bindtap="updateStatus"
            >
              重置为未开封
            </button>
          </view>
        </view>
      </scroll-view>

      <view class="detail-actions">
        <button class="btn btn-outline" bindtap="editStorage">编辑</button>
        <button class="btn btn-outline delete-btn" bindtap="deleteStorage">删除</button>
      </view>
    </view>
  </view>
</view>
