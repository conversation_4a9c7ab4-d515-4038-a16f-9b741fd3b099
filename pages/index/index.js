// pages/index/index.js
const app = getApp()

Page({
  data: {
    isLoggedIn: app.globalData.isLoggedIn,
    userInfo: null,
    loading: true,
    stats: {
      totalCoffees: 0,
      totalStorage: 0,
      recentActivity: 0
    },
    // 快捷添加相关数据
    showQuickAddModal: false,
    quickAddLoading: false,
    quickAddForm: {
      brand: '',
      name: '',
      origin: '',
      price: '',
      rating: 0,
      alsoPurchased: false,
      purchaseDate: '',
      weight: ''
    }
  },

  onLoad() {
    // this.checkLoginStatus()
    // await getApp().globalData.loginPromise;
    console.log("isLoggedIn", this.data.isLoggedIn);
    const checkInitialized = () => {
      if (app.globalData.isLaunchFinished) {
        this.initPage()
      } else {
        // 每 100ms 检查一次状态
        setTimeout(checkInitialized, 100);
      }
    };
    checkInitialized();
  },

  onShow() {
    // this.checkLoginStatus()
    if (this.data.isLoggedIn) {
      this.loadUserStats()
    }
  },

  // 初始化页面
  async initPage() {
    // if (this.data.loading) return
    // this.setData({ loading: true })
    try {
      this.setData({
        userInfo: app.globalData.userInfo,
        isLoggedIn: true,
        loading: false
      })

      // 加载用户统计数据
      this.loadUserStats()

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })

    } catch (error) {
      console.error('登录失败', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      const db = wx.cloud.database()

      // 获取咖啡图谱数量
      const coffeeAtlas = await db.collection('coffee_atlas').where({
        _openid: app.globalData.openid
      }).count()

      // 获取豆仓数量
      const coffeeStorage = await db.collection('coffee_storage').where({
        _openid: app.globalData.openid
      }).count()

      // 获取最近活动数量（最近 7 天）
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

      const recentActivity = await db.collection('coffee_storage').where({
        _openid: app.globalData.openid,
        createTime: db.command.gte(sevenDaysAgo)
      }).count()

      this.setData({
        stats: {
          totalCoffees: coffeeAtlas.total,
          totalStorage: coffeeStorage.total,
          recentActivity: recentActivity.total
        }
      })

    } catch (error) {
      console.error('加载统计数据失败', error)
    }
  },

  // 导航到咖啡图谱
  navigateToAtlas() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }
    wx.switchTab({
      url: '/pages/atlas/atlas'
    })
  },

  // 导航到豆仓
  navigateToStorage() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }
    wx.switchTab({
      url: '/pages/storage/storage'
    })
  },

  // 导航到个人中心
  navigateToProfile() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  // 跳转到添加咖啡页面
  navigateToAddCoffee() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: '/pages/add-coffee/add-coffee'
    })
  }
})
