/**pages/index/index.wxss**/

/* 未登录状态 */
.login-section {
  padding: 80rpx 48rpx;
  text-align: center;
}

.welcome-header {
  margin-bottom: 120rpx;
}

.coffee-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.welcome-title {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 16rpx;
}

.welcome-subtitle {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 48rpx;
}

.features {
  margin-bottom: 80rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  padding: 32rpx;
  background-color: var(--card-background);
  border-radius: 24rpx;
  box-shadow: var(--shadow);
}

.feature-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.feature-text {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.login-btn-area {
  margin-top: 48rpx;
}

.login-btn {
  width: 100%;
  font-size: 36rpx;
  padding: 32rpx;
}

/* 已登录状态 */
.dashboard {
  padding: 32rpx;
}

.user-header {
  padding: 40rpx 32rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 24rpx;
  box-shadow: var(--shadow);
  margin-bottom: 32rpx;
  text-align: center;
}

.user-greeting {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 12rpx;
}

.greeting-text {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-right: 8rpx;
}

.user-name {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

.user-welcome {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

/* 统计数据 */
.stats-section {
  margin-bottom: 32rpx;
}

.stats-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.stats-grid {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  flex: 1;
  background-color: var(--card-background);
  border-radius: 20rpx;
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: var(--shadow);
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: scale(0.95);
  background-color: var(--background-color);
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 快捷操作 */
.quick-actions {
  margin-bottom: 32rpx;
}

.actions-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.actions-grid {
  display: flex;
  gap: 16rpx;
}

.action-item {
  flex: 1;
  background-color: var(--card-background);
  border-radius: 20rpx;
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: var(--shadow);
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.action-item:active .action-icon,
.action-item:active .action-text {
  color: white;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  transition: color 0.3s ease;
}

.action-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 500;
  transition: color 0.3s ease;
}

/* 快捷添加部分 */
.quick-add-section {
  margin-bottom: 32rpx;
}

.quick-add-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.quick-add-btn:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 6rpx rgba(139, 69, 19, 0.2);
}

.add-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.add-text {
  font-size: 32rpx;
  font-weight: 600;
}


