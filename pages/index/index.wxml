<!--pages/index/index.wxml-->
<view class="container">
  <!-- 未登录状态 -->
  <view wx:if="{{!isLoggedIn}}" class="login-section">
    <view class="welcome-header">
      <view class="coffee-icon">☕</view>
      <view class="welcome-title">欢迎来到我的咖啡</view>
      <view class="welcome-subtitle">专业的咖啡豆管理工具</view>
    </view>

    <view class="features">
      <view class="feature-item">
        <view class="feature-icon">📊</view>
        <view class="feature-text">咖啡图谱管理</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">🏭</view>
        <view class="feature-text">豆仓存储记录</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">📊</view>
        <view class="feature-text">数据统计分析</view>
      </view>
    </view>

    <view class="login-btn-area">
      <button class="btn login-btn" bindtap="initPage" loading="{{loading}}">
        登录开始使用
      </button>
    </view>
  </view>

  <!-- 已登录状态 -->
  <view wx:else class="dashboard">
    <!-- 用户信息 -->
    <view class="user-header">
      <view class="user-greeting">
        <view class="greeting-text">你好，</view>
        <view class="user-name">{{userInfo.nickName}}</view>
      </view>
      <view class="user-welcome">开始你的咖啡之旅吧！</view>
    </view>

    <!-- 统计数据 -->
    <view class="stats-section">
      <view class="stats-title">我的数据</view>
      <view class="stats-grid">
        <view class="stat-item" bindtap="navigateToAtlas">
          <view class="stat-number">{{stats.totalCoffees}}</view>
          <view class="stat-label">咖啡图谱</view>
        </view>
        <view class="stat-item" bindtap="navigateToStorage">
          <view class="stat-number">{{stats.totalStorage}}</view>
          <view class="stat-label">豆仓存储</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{stats.recentActivity}}</view>
          <view class="stat-label">近期活动</view>
        </view>
      </view>
    </view>

    <!-- 快捷添加 -->
    <view class="quick-add-section">
      <button class="quick-add-btn" bindtap="navigateToAddCoffee">
        <view class="add-icon">➕</view>
        <view class="add-text">添加咖啡豆</view>
      </button>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="actions-title">快捷操作</view>
      <view class="actions-grid">
        <view class="action-item" bindtap="navigateToAtlas">
          <view class="action-icon">📊</view>
          <view class="action-text">咖啡图谱</view>
        </view>
        <view class="action-item" bindtap="navigateToStorage">
          <view class="action-icon">🏭</view>
          <view class="action-text">我的豆仓</view>
        </view>
        <view class="action-item" bindtap="navigateToProfile">
          <view class="action-icon">👤</view>
          <view class="action-text">个人中心</view>
        </view>
      </view>
    </view>
  </view>


</view>
